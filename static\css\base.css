/* ========================================
   RECIPE FINDER - BASE STYLES
   Foundation styles including reset, variables, typography, and global utilities
   ======================================== */

/* ========================================
   CSS CUSTOM PROPERTIES (DESIGN TOKENS)
   ======================================== */
:root {
  /* Brand Colors - Recipe Finder Theme */
  --primary-color: #10b981;
  /* Fresh green - suggests freshness and health */
  --secondary-color: #059669;
  /* Darker green for secondary elements */
  --accent-color: #047857;
  /* Deep green for accents and highlights */
  --neutral-color: #f0fdf4;
  /* Light green tint - feels fresh and clean */
  --primary-dark: #059669;
  /* Darker version of primary for hover states */
  --secondary-dark: #047857;
  /* Darker version of secondary for hover states */
  --text-on-primary: #FFFFFF;
  /* Text color on primary background */
  --text-on-secondary: #FFFFFF;
  /* Text color on secondary background */
  --text-dark: #333333;
  /* Dark text for general content */
  --text-light: #666666;
  /* Lighter text for secondary content */

  /* Design System Colors */
  --primary-50: #f0fdf4;
  --primary-100: #dcfce7;
  --primary-200: #bbf7d0;
  --primary-300: #86efac;
  --primary-400: #4ade80;
  --primary-500: #10b981;
  --primary-600: #059669;
  --primary-700: #047857;
  --primary-800: #065f46;
  --primary-900: #064e3b;


  /* Spacing Scale */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;
}

/* ========================================
   CSS RESET & NORMALIZE
   ======================================== */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  * {
    -webkit-tap-highlight-color: transparent;
  }

  body {
    font-size: 16px; /* Prevent zoom on iOS */
    -webkit-text-size-adjust: 100%;
  }
}

/* ========================================
   TYPOGRAPHY
   ======================================== */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 1rem 0;
  font-weight: var(--font-weight-semibold);
  line-height: 1.2;
  color: var(--text-dark);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-base);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* ========================================
   GLOBAL LAYOUT UTILITIES
   ======================================== */
.body-with-sidebar {
  display: flex;
  min-height: 100vh;
  background-color: #fff;
}

.main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  margin-left: 70px; /* Account for sidebar width */
}

/* Mobile layout adjustments */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    min-height: calc(100vh - 60px); /* Account for footer height */
    padding-bottom: 100px; /* Add space for scrolling past mobile bottom nav */
  }
}

/* ========================================
   UTILITY CLASSES
   ======================================== */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-dark { color: var(--text-dark); }
.text-light { color: var(--text-light); }

.bg-surface { background-color: var(--surface); }
.bg-primary { background-color: var(--primary-500); }
.bg-neutral { background-color: var(--neutral-color); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Flexbox utilities */
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-grow-1 { flex-grow: 1; }
.justify-content-center { justify-content: center; }
.align-items-center { align-items: center; }

/* Spacing utilities */
.m-0 { margin: 0; }
.p-0 { padding: 0; }
.mt-auto { margin-top: auto; }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }

/* Responsive utilities */
.w-100 { width: 100%; }
.min-vh-100 { min-height: 100vh; }

/* Text alignment */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
