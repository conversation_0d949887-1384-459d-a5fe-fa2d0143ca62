"""
Cache utilities for robust Redis operations with error handling and fallback mechanisms.
Provides safe cache operations that gracefully handle Redis connection failures.
"""

import logging
from typing import Any, Optional
from django.core.cache import cache
from redis.exceptions import ConnectionError as RedisConnectionError, TimeoutError as RedisTimeoutError
from django_redis.exceptions import ConnectionInterrupted

# Set up logger for cache operations
logger = logging.getLogger('recipes.cache')

# Cache duration constants (in seconds)
RECIPE_CACHE_DURATION = 86400  # 1 day for recipe data
API_RATE_LIMIT_CACHE_DURATION = 86400  # 24 hours for API rate limiting


def safe_cache_get(cache_key: str, default: Any = None) -> Any:
    """
    Safely get a value from Redis cache with comprehensive error handling.

    Args:
        cache_key: The cache key to retrieve
        default: Default value to return if key not found or on error

    Returns:
        Cached value if found and accessible, otherwise default value
    """
    try:
        result = cache.get(cache_key, default)
        if result is not None and result != default:
            logger.debug(f"Cache HIT for key: {cache_key}")
        else:
            logger.debug(f"Cache MISS for key: {cache_key}")
        return result
    except (RedisConnectionError, RedisTimeoutError, ConnectionInterrupted) as e:
        logger.warning(f"Redis cache get failed for key '{cache_key}': {e}")
        return default
    except Exception as e:
        logger.error(f"Unexpected cache get error for key '{cache_key}': {e}")
        return default


def safe_cache_set(cache_key: str, value: Any, timeout: Optional[int] = None) -> bool:
    """
    Safely set a value in Redis cache with comprehensive error handling.

    Args:
        cache_key: The cache key to set
        value: The value to cache
        timeout: Cache timeout in seconds (None for default)

    Returns:
        True if successfully cached, False otherwise
    """
    try:
        cache.set(cache_key, value, timeout=timeout)
        logger.debug(f"Cache SET successful for key: {cache_key}")
        return True
    except (RedisConnectionError, RedisTimeoutError, ConnectionInterrupted) as e:
        logger.warning(f"Redis cache set failed for key '{cache_key}': {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected cache set error for key '{cache_key}': {e}")
        return False


def safe_cache_delete(cache_key: str) -> bool:
    """
    Safely delete a value from Redis cache with error handling.

    Args:
        cache_key: The cache key to delete

    Returns:
        True if successfully deleted or key didn't exist, False on error
    """
    try:
        cache.delete(cache_key)
        logger.debug(f"Cache DELETE successful for key: {cache_key}")
        return True
    except (RedisConnectionError, RedisTimeoutError, ConnectionInterrupted) as e:
        logger.warning(f"Redis cache delete failed for key '{cache_key}': {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected cache delete error for key '{cache_key}': {e}")
        return False


def check_redis_health() -> dict:
    """
    Check Redis connection health and return status information.

    Returns:
        Dictionary with health status information
    """
    health_status = {
        'redis_available': False,
        'connection_time': None,
        'error': None,
        'cache_backend': None
    }

    try:
        import time
        from django_redis import get_redis_connection
        from django.conf import settings

        # Record connection attempt time
        start_time = time.time()

        # Get Redis connection and test it
        redis_conn = get_redis_connection("default")
        redis_conn.ping()

        # Calculate connection time
        connection_time = time.time() - start_time

        # Update status
        health_status.update({
            'redis_available': True,
            'connection_time': round(connection_time * 1000, 2),  # Convert to milliseconds
            'cache_backend': settings.CACHES['default']['BACKEND']
        })

        logger.info(f"Redis health check passed in {health_status['connection_time']}ms")

    except (RedisConnectionError, RedisTimeoutError, ConnectionInterrupted) as e:
        health_status['error'] = f"Redis connection error: {str(e)}"
        logger.warning(f"Redis health check failed: {e}")
    except ImportError as e:
        health_status['error'] = f"Redis client not available: {str(e)}"
        logger.error(f"Redis import error: {e}")
    except Exception as e:
        health_status['error'] = f"Unexpected error: {str(e)}"
        logger.error(f"Unexpected Redis health check error: {e}")

    return health_status


def get_cache_stats() -> dict:
    """
    Get Redis cache statistics and information.

    Returns:
        Dictionary with cache statistics
    """
    stats = {
        'redis_available': False,
        'memory_usage': None,
        'connected_clients': None,
        'total_commands_processed': None,
        'keyspace_hits': None,
        'keyspace_misses': None,
        'hit_rate': None,
        'error': None
    }

    try:
        from django_redis import get_redis_connection

        redis_conn = get_redis_connection("default")
        info = redis_conn.info()

        # Extract relevant statistics
        stats.update({
            'redis_available': True,
            'memory_usage': info.get('used_memory_human'),
            'connected_clients': info.get('connected_clients'),
            'total_commands_processed': info.get('total_commands_processed'),
            'keyspace_hits': info.get('keyspace_hits', 0),
            'keyspace_misses': info.get('keyspace_misses', 0),
        })

        # Calculate hit rate
        hits = stats['keyspace_hits']
        misses = stats['keyspace_misses']
        if hits + misses > 0:
            stats['hit_rate'] = round((hits / (hits + misses)) * 100, 2)

        logger.debug("Cache statistics retrieved successfully")

    except (RedisConnectionError, RedisTimeoutError, ConnectionInterrupted) as e:
        stats['error'] = f"Redis connection error: {str(e)}"
        logger.warning(f"Failed to get cache stats: {e}")
    except Exception as e:
        stats['error'] = f"Unexpected error: {str(e)}"
        logger.error(f"Unexpected error getting cache stats: {e}")

    return stats


def invalidate_cache_pattern(pattern: str) -> dict:
    """
    Invalidate cache entries matching a pattern using Redis SCAN.

    Args:
        pattern: Redis key pattern (e.g., "search_*", "*recipe*")

    Returns:
        Dictionary with invalidation results
    """
    result = {
        'success': False,
        'keys_deleted': 0,
        'error': None
    }

    try:
        from django_redis import get_redis_connection

        redis_conn = get_redis_connection("default")

        # Use SCAN to find matching keys (more efficient than KEYS)
        deleted_count = 0
        cursor = 0

        while True:
            cursor, keys = redis_conn.scan(cursor=cursor, match=pattern, count=100)
            if keys:
                redis_conn.delete(*keys)
                deleted_count += len(keys)

            if cursor == 0:
                break

        result.update({
            'success': True,
            'keys_deleted': deleted_count
        })

        logger.info(f"Invalidated {deleted_count} cache entries matching pattern '{pattern}'")

    except (RedisConnectionError, RedisTimeoutError, ConnectionInterrupted) as e:
        result['error'] = f"Redis connection error: {str(e)}"
        logger.warning(f"Cache invalidation failed for pattern '{pattern}': {e}")
    except Exception as e:
        result['error'] = f"Unexpected error: {str(e)}"
        logger.error(f"Unexpected error during cache invalidation for pattern '{pattern}': {e}")

    return result


def warm_cache_for_popular_recipes(recipe_ids: list) -> dict:
    """
    Pre-warm cache for popular recipes to improve performance.

    Args:
        recipe_ids: List of recipe IDs to warm up

    Returns:
        Dictionary with warming results
    """
    result = {
        'success': False,
        'recipes_warmed': 0,
        'errors': [],
        'total_recipes': len(recipe_ids)
    }

    try:
        # This would be implemented with actual recipe fetching logic
        # For now, we'll just log the intent
        logger.info(f"Cache warming requested for {len(recipe_ids)} recipes")

        # TODO: Implement actual cache warming logic
        # This would involve calling the recipe API functions for each ID
        # to populate the cache proactively

        result.update({
            'success': True,
            'recipes_warmed': 0  # Would be updated with actual implementation
        })

    except Exception as e:
        result['errors'].append(f"Cache warming error: {str(e)}")
        logger.error(f"Cache warming failed: {e}")

    return result