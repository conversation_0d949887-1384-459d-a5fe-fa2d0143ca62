#!/usr/bin/env python
"""
Debug script to see what keys are actually in Redis.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'recipe_finder.settings')
django.setup()

from django_redis import get_redis_connection

def debug_redis_keys():
    print("=== Debugging Redis Keys ===\n")
    
    try:
        redis_conn = get_redis_connection("default")
        
        # Get all keys
        all_keys = redis_conn.keys("*")
        print(f"Total keys in Redis: {len(all_keys)}")
        
        if all_keys:
            print("\nAll keys in Redis:")
            for key in all_keys:
                key_str = key.decode('utf-8') if isinstance(key, bytes) else str(key)
                print(f"  {key_str}")
                
                # Get the value to see what's stored
                try:
                    value = redis_conn.get(key)
                    if value:
                        value_str = value.decode('utf-8') if isinstance(value, bytes) else str(value)
                        print(f"    Value length: {len(value_str)} characters")
                except Exception as e:
                    print(f"    Error reading value: {e}")
        else:
            print("\nNo keys found in Redis")
        
        # Test pattern matching
        print(f"\n=== Testing Pattern Matching ===")
        patterns = [
            "*search_recipes*",
            "recipe_finder*",
            "*chicken*",
            "*:1:*"
        ]
        
        for pattern in patterns:
            matching_keys = redis_conn.keys(pattern)
            print(f"Pattern '{pattern}': {len(matching_keys)} matches")
            for key in matching_keys:
                key_str = key.decode('utf-8') if isinstance(key, bytes) else str(key)
                print(f"  {key_str}")
        
    except Exception as e:
        print(f"Error accessing Redis: {e}")
        return False
    
    return True

if __name__ == "__main__":
    debug_redis_keys()
