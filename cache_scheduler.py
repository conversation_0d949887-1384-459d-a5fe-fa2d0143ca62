#!/usr/bin/env python
"""
Standalone cache scheduler for Recipe Finder.
Runs continuously and clears cache on the last day of each month.
"""

import os
import sys
import time
import schedule
import subprocess
from datetime import datetime, timedelta
import calendar
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cache_scheduler.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def is_last_day_of_month():
    """Check if today is the last day of the current month."""
    today = datetime.now().date()
    last_day = calendar.monthrange(today.year, today.month)[1]
    return today.day == last_day

def clear_cache():
    """Execute the cache clearing command."""
    try:
        logger.info("Starting scheduled cache clearing...")
        
        # Change to the project directory
        project_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(project_dir)
        
        # Run the cache clearing command
        result = subprocess.run([
            sys.executable, 'manage.py', 'auto_clear_monthly_cache', '--force'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info(f"Cache clearing completed successfully: {result.stdout}")
        else:
            logger.error(f"Cache clearing failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        logger.error("Cache clearing command timed out")
    except Exception as e:
        logger.error(f"Error during cache clearing: {e}")

def scheduled_cache_clear():
    """Wrapper function for scheduled cache clearing."""
    if is_last_day_of_month():
        clear_cache()
    else:
        logger.info("Not the last day of month, skipping cache clear")

def main():
    """Main scheduler loop."""
    logger.info("Recipe Finder Cache Scheduler started")
    logger.info("Will clear cache on the last day of each month at midnight")
    
    # Schedule cache clearing for midnight on last day of month
    # Check every day at midnight if it's the last day
    schedule.every().day.at("00:00").do(scheduled_cache_clear)
    
    # Also schedule a daily check at 1 AM as backup
    schedule.every().day.at("01:00").do(scheduled_cache_clear)
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
            
    except KeyboardInterrupt:
        logger.info("Scheduler stopped by user")
    except Exception as e:
        logger.error(f"Scheduler error: {e}")

if __name__ == "__main__":
    main()
