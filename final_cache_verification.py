#!/usr/bin/env python
"""
Final comprehensive verification of Redis caching implementation.
"""

import os
import sys
import django
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'recipe_finder.settings')
django.setup()

from recipes.api_manager import search_recipes, search_recipes_by_cuisine, search_recipes_by_name, search_recipes_by_diet
from recipes.cache_utils import check_redis_health, get_cache_stats, invalidate_cache_pattern

def final_verification():
    print("🧪 === FINAL REDIS CACHING VERIFICATION ===\n")
    
    # 1. Redis Health Check
    print("1️⃣ Redis Health Check...")
    health = check_redis_health()
    if health['redis_available']:
        print(f"   ✅ Redis is healthy (connection: {health['connection_time']}ms)")
    else:
        print(f"   ❌ Redis is not available: {health['error']}")
        return False
    
    # 2. Initial Cache Stats
    print("\n2️⃣ Initial Cache Statistics...")
    initial_stats = get_cache_stats()
    if initial_stats['redis_available']:
        initial_hits = initial_stats.get('keyspace_hits', 0)
        initial_misses = initial_stats.get('keyspace_misses', 0)
        initial_rate = initial_stats.get('hit_rate', 0)
        print(f"   📊 Initial hit rate: {initial_rate}%")
        print(f"   📊 Initial hits: {initial_hits}, misses: {initial_misses}")
    
    # 3. Test Different Search Types with Timing
    print("\n3️⃣ Testing Different Search Types...")
    
    search_tests = [
        ("Basic Search", lambda: search_recipes('pizza')),
        ("Cuisine Search", lambda: search_recipes_by_cuisine('Mexican')),
        ("Name Search", lambda: search_recipes_by_name('burger')),
        ("Diet Search", lambda: search_recipes_by_diet('vegetarian')),
    ]
    
    timing_results = []
    
    for test_name, search_func in search_tests:
        print(f"\n   🔍 Testing {test_name}...")
        
        # First call (should hit API)
        start_time = time.time()
        results1 = search_func()
        first_time = time.time() - start_time
        
        # Second call (should hit cache)
        start_time = time.time()
        results2 = search_func()
        second_time = time.time() - start_time
        
        # Verify results are identical
        if results1 == results2:
            print(f"      ✅ Results identical ({len(results1)} recipes)")
        else:
            print(f"      ❌ Results differ!")
            return False
        
        # Check timing improvement
        if second_time < first_time * 0.2:  # Should be much faster
            speedup = first_time / second_time if second_time > 0 else float('inf')
            print(f"      ⚡ Cache speedup: {speedup:.1f}x ({first_time:.2f}s → {second_time:.3f}s)")
            timing_results.append((test_name, speedup, True))
        else:
            print(f"      ⚠️  No significant speedup ({first_time:.2f}s → {second_time:.2f}s)")
            timing_results.append((test_name, 1.0, False))
    
    # 4. Final Cache Stats
    print("\n4️⃣ Final Cache Statistics...")
    final_stats = get_cache_stats()
    if final_stats['redis_available']:
        final_hits = final_stats.get('keyspace_hits', 0)
        final_misses = final_stats.get('keyspace_misses', 0)
        final_rate = final_stats.get('hit_rate', 0)
        
        hits_gained = final_hits - initial_hits
        misses_gained = final_misses - initial_misses
        
        print(f"   📊 Final hit rate: {final_rate}%")
        print(f"   📊 Cache hits gained: {hits_gained}")
        print(f"   📊 Cache misses gained: {misses_gained}")
        print(f"   📊 Memory usage: {final_stats.get('memory_usage', 'N/A')}")
    
    # 5. Test Cache Invalidation
    print("\n5️⃣ Testing Cache Invalidation...")
    result = invalidate_cache_pattern("*search_recipes*")
    if result['success']:
        print(f"   🗑️  Successfully invalidated {result['keys_deleted']} search_recipes entries")
    else:
        print(f"   ❌ Cache invalidation failed: {result['error']}")
    
    # 6. Verify Invalidation Worked
    print("\n6️⃣ Verifying Cache Invalidation...")
    start_time = time.time()
    fresh_results = search_recipes('pizza')  # Should hit API again
    fresh_time = time.time() - start_time
    
    if fresh_time > 0.5:  # Should be slower (hitting API)
        print(f"   ✅ Cache invalidation verified - fresh API call took {fresh_time:.2f}s")
    else:
        print(f"   ⚠️  Cache invalidation unclear - call took {fresh_time:.2f}s")
    
    # 7. Summary Report
    print("\n📋 === VERIFICATION SUMMARY ===")
    print(f"✅ Redis Health: {'PASS' if health['redis_available'] else 'FAIL'}")
    print(f"✅ Cache Statistics: {'PASS' if final_stats['redis_available'] else 'FAIL'}")
    
    cache_working_count = sum(1 for _, _, working in timing_results if working)
    print(f"✅ Cache Performance: {cache_working_count}/{len(timing_results)} search types showing speedup")
    
    for test_name, speedup, working in timing_results:
        status = "✅ PASS" if working else "⚠️  UNCLEAR"
        print(f"   {status} {test_name}: {speedup:.1f}x speedup")
    
    print(f"✅ Cache Invalidation: {'PASS' if result['success'] else 'FAIL'}")
    
    # Overall assessment
    all_passed = (
        health['redis_available'] and
        final_stats['redis_available'] and
        cache_working_count >= len(timing_results) * 0.75 and  # At least 75% working
        result['success']
    )
    
    if all_passed:
        print(f"\n🎉 OVERALL RESULT: ✅ ALL TESTS PASSED")
        print(f"   Redis caching is working correctly with 30-day aggressive caching!")
        print(f"   Cache hit rate improved to {final_rate}%")
        print(f"   Average speedup: {sum(s for _, s, w in timing_results if w) / max(1, cache_working_count):.1f}x")
    else:
        print(f"\n❌ OVERALL RESULT: SOME ISSUES DETECTED")
        print(f"   Please review the individual test results above")
    
    return all_passed

if __name__ == "__main__":
    success = final_verification()
    sys.exit(0 if success else 1)
