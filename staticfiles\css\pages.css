/* ========================================
   RECIPE FINDER - PAGE-SPECIFIC STYLES
   Hero sections, search results, recipe layouts, discover page, and page-specific responsive behavior
   ======================================== */

/* ========================================
   HERO SECTIONS
   ======================================== */
.center-hero-container {
  min-height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* Mobile-specific styling for center-hero-container - remove ALL spacing */
@media (max-width: 768px) {
  .center-hero-container {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    /* Override any inherited spacing */
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.main-hero-title {
  font-size: 2.2rem;
  font-weight: 600;
  color: #FF6B35;
  letter-spacing: -1px;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .main-hero-title {
    font-size: 2.8rem;
  }
}

@media (max-width: 768px) {
  .main-hero-title {
    font-size: 2rem !important;
  }
}

.hero-section {
  transition: all var(--transition-slow);
}

.hero-section .logo {
  max-width: 250px;
  margin-bottom: 1rem;
}

.hero-section .lead {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: 2rem;
}

/* Force hero section visibility on mobile */
@media (max-width: 768px) {
  #hero-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  #hero-section .logo,
  #hero-section .main-hero-title,
  #hero-section .lead {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  #hero-section .main-hero-title {
    font-size: 1.8rem !important;
    margin-bottom: 1rem !important;
  }

  #hero-section .logo {
    max-width: 180px !important;
    margin-bottom: 1rem !important;
  }
}

/* ========================================
   SEARCH RESULTS LAYOUTS
   ======================================== */

/* Recipe results container should fill available space */
#recipe-results {
  flex: 1;
  width: 100%;
  min-height: 100%;
  transition: all var(--transition-slow);
}

/* Results section should fill container */
.results-section {
  min-height: 100%;
  width: 100%;
}

/* Results row should fill available space */
#results {
  flex: 1;
  display: flex;
  flex-direction: column;
}

#results-row {
  flex: 1;
  display: flex !important;
}

#results-row .col-12 {
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Top Navigation Bar (shown after search) */
.top-nav-bar {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e5e7eb;
  padding: var(--space-3) 0;
  z-index: 100;
  transition: transform var(--transition-slow);
}

.top-nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.top-nav-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
  margin: 0;
  text-align: center;
}

/* Full-screen results expansion */
.recipe-results-expanded {
  overflow: hidden;
}

.recipe-results-expanded #recipe-results {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: #fff;
  overflow-y: auto;
  padding: var(--space-4);
  margin: 0;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Enhanced scroll indicators for expanded view */
body.recipe-results-expanded .carousel {
  scrollbar-width: thin;
  scrollbar-color: var(--secondary-color) #f1f1f1;
}

body.recipe-results-expanded .carousel::-webkit-scrollbar {
  height: 8px;
  display: block;
}

body.recipe-results-expanded .carousel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

body.recipe-results-expanded .carousel::-webkit-scrollbar-thumb {
  background: var(--secondary-color);
  border-radius: 4px;
}

/* Recipe grid layouts */
.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

/* Horizontal carousel for featured recipes */
.carousel {
  display: flex;
  overflow-x: auto;
  gap: var(--space-4);
  padding: var(--space-4) 0;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.carousel::-webkit-scrollbar {
  height: 6px;
}

.carousel::-webkit-scrollbar-track {
  background: transparent;
}

.carousel::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

/* Mobile search active state - remove all spacing */
@media (max-width: 768px) {
  body.mobile-search-active .search-container {
    padding: 0 !important;
    margin: 0 !important;
  }

  body.mobile-search-active .search-form-wrapper {
    padding: 0 !important;
    margin: 0 !important;
  }

  body.mobile-search-active .quick-filters {
    margin: 0 !important;
    padding: 0 !important;
  }

  body.mobile-search-active #results {
    padding: 0 !important;
    margin: 0 !important;
    margin-top: 0 !important;
  }

  /* Remove spacing from recipe cards and containers */
  body.mobile-search-active .recipe-card,
  body.mobile-search-active .card,
  body.mobile-search-active .card-body,
  body.mobile-search-active .card-header,
  body.mobile-search-active .card-footer {
    margin: 0 !important;
    padding: 5px !important; /* Minimal padding for readability */
  }

  /* Remove spacing from titles and text elements */
  body.mobile-search-active h1,
  body.mobile-search-active h2,
  body.mobile-search-active h3,
  body.mobile-search-active h4,
  body.mobile-search-active h5,
  body.mobile-search-active h6,
  body.mobile-search-active p,
  body.mobile-search-active .lead {
    margin: 0 !important;
    padding: 2px 0 !important; /* Minimal vertical spacing for readability */
  }

  /* Mobile scroll expansion styles */
  body.recipe-results-expanded {
    overflow: hidden !important;
  }

  body.recipe-results-expanded #recipe-results {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 1000 !important;
    background-color: #fff !important;
    overflow-y: auto !important;
    padding: var(--space-3) !important;
    margin: 0 !important;
  }

  .recipes-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }
}

/* Landscape orientation fixes */
@media (max-width: 768px) and (orientation: landscape) {
  .center-hero-container {
    min-height: 100vh !important;
  }

  .main-hero-title {
    font-size: 1.5rem !important;
    margin-bottom: var(--space-4) !important;
  }
}

/* ========================================
   DISCOVER PAGE STYLES
   ======================================== */
.discover-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  background: var(--neutral-color);
  min-height: 100vh;
}

.discover-header {
  text-align: center;
  margin-bottom: 30px;
}

.discover-header h1 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.featured-recipe {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all var(--transition-slow);
}

.featured-recipe:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.featured-recipe-image {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.featured-recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px 0 0 15px;
}

.featured-recipe-info {
  flex: 1;
  padding: 20px;
}

.featured-recipe-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-dark);
}

.featured-recipe-details {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.featured-recipe-description {
  color: var(--text-light);
  font-size: 0.9rem;
  line-height: 1.4;
}

.randomize-btn {
  background: var(--secondary-color);
  color: var(--text-on-secondary);
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all var(--transition-slow);
  margin-bottom: 30px;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.randomize-btn:hover {
  background: var(--secondary-dark);
  transform: translateY(-2px);
}

.randomize-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.categories-strip {
  display: flex;
  gap: 15px;
  margin-bottom: 40px;
  overflow-x: auto;
  padding: 10px 0;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) transparent;
}

.categories-strip::-webkit-scrollbar {
  height: 6px;
}

.categories-strip::-webkit-scrollbar-track {
  background: transparent;
}

.categories-strip::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

.category-chip {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  padding: 8px 20px;
  cursor: pointer;
  transition: all var(--transition-slow);
  white-space: nowrap;
  font-size: 0.9rem;
  color: var(--text-dark);
  text-decoration: none;
}

.category-chip:hover,
.category-chip.active {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: var(--text-on-primary);
  text-decoration: none;
}

.recipe-info {
  padding: 15px;
}

.recipe-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-dark);
}

.recipe-area {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.recipe-category {
  background: var(--neutral-color);
  color: var(--text-dark);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  display: inline-block;
  border: 1px solid var(--primary-color);
}

.loading {
  text-align: center;
  padding: 40px;
  color: var(--text-light);
  font-size: 1.1rem;
}

.error-message {
  text-align: center;
  padding: 20px;
  color: var(--accent-color);
  background: rgba(198, 40, 40, 0.1);
  border-radius: 10px;
  margin-bottom: 20px;
}

/* Discover page mobile responsive */
@media (max-width: 768px) {
  .discover-header h1 {
    font-size: 2rem;
  }

  .featured-recipe {
    flex-direction: column;
    text-align: center;
  }

  .featured-recipe-image {
    width: 100%;
    height: 200px;
  }

  .featured-recipe-image img {
    border-radius: 15px 15px 0 0;
  }

  .categories-strip {
    justify-content: flex-start;
  }
}

/* Loading spinner for randomize button */
.randomize-btn.htmx-request::after {
  content: "🔄";
  animation: spin 1s linear infinite;
  margin-left: 5px;
}

/* ========================================
   RECIPE DETAIL PAGE STYLES
   ======================================== */
.recipe-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-5);
}

.recipe-image-container {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  transition: transform var(--transition-slow);
  box-shadow: var(--shadow-md);
}

.recipe-image-container:hover {
  transform: scale(1.02);
}

.recipe-image-placeholder {
  background: var(--surface);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  height: 300px;
}

.instruction-number {
  font-weight: bold;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  min-width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-3);
  flex-shrink: 0;
}

.recipe-detail-card {
  border: none;
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--space-4);
}

.recipe-detail-card .card-header {
  background: var(--accent-color);
  color: white;
  border-bottom: none;
  padding: var(--space-4);
}

.recipe-detail-card .card-body {
  padding: var(--space-4);
}

.recipe-quick-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-4);
  margin: var(--space-4) 0;
}

.recipe-quick-info .card {
  text-align: center;
  border: none;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.recipe-quick-info .card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.recipe-quick-info .card-body {
  padding: var(--space-4);
}

.recipe-quick-info i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--space-2);
}

.ingredients-list {
  list-style: none;
  padding: 0;
}

.ingredients-list li {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
}

.ingredients-list li:last-child {
  border-bottom: none;
}

.ingredients-list li::before {
  content: "🥄";
  margin-right: var(--space-2);
  font-size: 1.2rem;
}

.instructions-list {
  list-style: none;
  padding: 0;
}

.instructions-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--space-4);
  padding: var(--space-3);
  background: var(--surface);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-color);
}

.instructions-list li:last-child {
  margin-bottom: 0;
}

/* Recipe detail mobile responsive */
@media (max-width: 768px) {
  .recipe-detail-container {
    padding: var(--space-3);
  }

  .recipe-quick-info {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-2);
  }

  .recipe-quick-info .card-body {
    padding: var(--space-2);
  }

  .recipe-quick-info i {
    font-size: 1.5rem;
  }

  .instruction-number {
    min-width: 25px;
    height: 25px;
    font-size: 0.9rem;
  }
}
