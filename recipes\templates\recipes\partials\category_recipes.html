{% if error %}
    <div class="loading">
        {{ error }}
    </div>
{% elif recipes %}
    {% for recipe in recipes %}
        <a href="{% url 'recipes:recipe_detail_api' recipe_id=recipe.id %}" class="recipe-card">
            <div class="recipe-image">
                {% if recipe.image %}
                    <img src="{{ recipe.image }}" alt="{{ recipe.title }}" loading="lazy">
                {% else %}
                    <span>🍽️ {{ recipe.title|truncatechars:20 }}</span>
                {% endif %}
            </div>
            <div class="recipe-info">
                <div class="recipe-title">{{ recipe.title|truncatechars:30 }}</div>
                <div class="recipe-area">{{ recipe.area|default:"International" }}</div>
                <div class="recipe-category">{{ recipe.category|default:category }}</div>
            </div>
        </a>
    {% endfor %}
{% else %}
    <div class="loading">
        No {{ category }} recipes found
    </div>
{% endif %}
