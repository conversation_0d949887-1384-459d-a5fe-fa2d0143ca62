"""
Django management command to manually clear recipe cache.
Usage: python manage.py clear_recipe_cache [--pattern=search_*] [--confirm]
"""

from django.core.management.base import BaseCommand, CommandError
from django.core.cache import cache
import os
import glob
from django.conf import settings


class Command(BaseCommand):
    help = 'Clear recipe cache manually. Use --pattern to clear specific cache keys.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--pattern',
            type=str,
            help='Cache key pattern to clear (e.g., "search_*", "themealdb_*"). If not provided, clears all recipe cache.',
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Skip confirmation prompt',
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show cache statistics before clearing',
        )

    def handle(self, *args, **options):
        pattern = options.get('pattern')
        confirm = options.get('confirm')
        show_stats = options.get('stats')

        # Show cache statistics if requested
        if show_stats:
            self.show_cache_stats()

        # Determine what to clear
        if pattern:
            cache_description = f"cache entries matching pattern '{pattern}'"
        else:
            cache_description = "ALL recipe cache entries"

        # Confirmation prompt
        if not confirm:
            response = input(f"Are you sure you want to clear {cache_description}? [y/N]: ")
            if response.lower() not in ['y', 'yes']:
                self.stdout.write(self.style.WARNING('Cache clearing cancelled.'))
                return

        # Clear cache
        try:
            if pattern:
                cleared_count = self.clear_cache_by_pattern(pattern)
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully cleared {cleared_count} cache entries matching pattern "{pattern}"'
                    )
                )
            else:
                self.clear_all_recipe_cache()
                self.stdout.write(
                    self.style.SUCCESS('Successfully cleared all recipe cache entries')
                )
        except Exception as e:
            raise CommandError(f'Error clearing cache: {e}')

    def show_cache_stats(self):
        """Show cache statistics."""
        self.stdout.write(self.style.HTTP_INFO('Cache Statistics:'))
        
        # For file-based cache, count files in cache directory
        cache_location = getattr(settings, 'CACHES', {}).get('default', {}).get('LOCATION')
        if cache_location and os.path.exists(cache_location):
            cache_files = glob.glob(os.path.join(cache_location, '*.djcache'))
            total_size = sum(os.path.getsize(f) for f in cache_files)
            
            self.stdout.write(f'  Total cache files: {len(cache_files)}')
            self.stdout.write(f'  Total cache size: {self.format_bytes(total_size)}')
            self.stdout.write(f'  Cache location: {cache_location}')
        else:
            self.stdout.write('  Cache statistics not available for this cache backend')
        
        self.stdout.write('')

    def clear_cache_by_pattern(self, pattern):
        """Clear cache entries matching a pattern."""
        # For file-based cache, we need to work with the file system
        cache_location = getattr(settings, 'CACHES', {}).get('default', {}).get('LOCATION')
        if not cache_location or not os.path.exists(cache_location):
            raise CommandError('Cache location not found or not accessible')

        # This is a simplified pattern matching for demonstration
        # In a production environment, you might want more sophisticated pattern matching
        cleared_count = 0
        
        # For now, we'll clear all cache since Django's file-based cache doesn't 
        # provide easy pattern-based clearing
        self.stdout.write(
            self.style.WARNING(
                f'Pattern-based clearing not fully implemented for file-based cache. '
                f'Clearing all cache instead.'
            )
        )
        cache.clear()
        
        # Count files that would match the pattern (for reporting)
        cache_files = glob.glob(os.path.join(cache_location, '*.djcache'))
        cleared_count = len(cache_files)
        
        return cleared_count

    def clear_all_recipe_cache(self):
        """Clear all cache entries."""
        cache.clear()

    def format_bytes(self, bytes_value):
        """Format bytes into human readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"
