{% extends 'recipes/base.html' %}
{% load static %}

{% block title %}{{ recipe.title }} - Recipe finder{% endblock %}

{% block content %}
<div class="container my-5">
  <div class="row">
    <!-- Back button -->
    <div class="col-12 mb-4">
      <a href="javascript:history.back()" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Back to Results
      </a>
    </div>

    <!-- Recipe header -->
    <div class="col-12 mb-4">
      <div class="d-flex align-items-center justify-content-between flex-wrap">
        <h1 class="mb-0" style="color: var(--text-primary);">{{ recipe.title }}</h1>
        <span class="badge" style="background-color: var(--primary-color); color: white; font-size: 1rem;">{{ recipe.source }}</span>
      </div>
      <hr class="my-3" style="border-color: var(--border);">
    </div>

    <!-- Recipe image and quick info -->
    <div class="col-md-6 mb-4">
      {% if recipe.image_url %}
      <div class="recipe-image-container rounded shadow overflow-hidden">
        <img src="{{ recipe.image_url }}" alt="{{ recipe.title }}" class="img-fluid w-100"
          style="object-fit: cover; max-height: 400px;">
      </div>
      {% else %}
      <div class="recipe-image-placeholder bg-light d-flex align-items-center justify-content-center rounded shadow"
        style="height: 300px;">
        <span class="text-muted">No Image Available</span>
      </div>
      {% endif %}

      <!-- Quick info cards -->
      <div class="row mt-4">
        {% if recipe.cooking_time %}
        <div class="col-6 col-md-4 mb-3">
          <div class="card h-100 border-0" style="box-shadow: var(--shadow-sm); border-radius: var(--radius-lg);">
            <div class="card-body text-center" style="padding: var(--space-4);">
              <i class="bi bi-clock fs-4" style="color: var(--primary-color);"></i>
              <h6 class="mt-2 mb-0" style="color: var(--text-primary); font-weight: var(--font-weight-semibold);">Cooking Time</h6>
              <p class="mb-0" style="color: var(--text-secondary);">{{ recipe.cooking_time }} min</p>
            </div>
          </div>
        </div>
        {% endif %}

        {% if recipe.servings %}
        <div class="col-6 col-md-4 mb-3">
          <div class="card h-100 border-0" style="box-shadow: var(--shadow-sm); border-radius: var(--radius-lg);">
            <div class="card-body text-center" style="padding: var(--space-4);">
              <i class="bi bi-people-fill fs-4" style="color: var(--primary-color);"></i>
              <h6 class="mt-2 mb-0" style="color: var(--text-primary); font-weight: var(--font-weight-semibold);">Servings</h6>
              <p class="mb-0" style="color: var(--text-secondary);">{{ recipe.servings }}</p>
            </div>
          </div>
        </div>
        {% endif %}

        {% if nutrition.nutrition %}
        <div class="col-6 col-md-4 mb-3">
          <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
              <i class="bi bi-lightning-charge-fill text-primary fs-4"></i>
              <h6 class="mt-2 mb-0">Calories</h6>
              <p class="mb-0">
                {% if nutrition.nutrition.calories %}
                {{ nutrition.nutrition.calories }}
                {% elif nutrition.nutrition.Calories %}
                {{ nutrition.nutrition.Calories }}
                {% else %}
                N/A
                {% endif %}
              </p>
            </div>
          </div>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- Recipe ingredients and instructions -->
    <div class="col-md-6">
      <!-- Ingredients -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
          <h4 class="mb-0">Ingredients</h4>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            {% for ingredient in recipe.ingredients %}
            <li class="list-group-item border-0 d-flex align-items-center">
              <i class="bi bi-check-circle-fill text-secondary me-2"></i>
              {{ ingredient }}
            </li>
            {% empty %}
            <li class="list-group-item border-0">No ingredients available</li>
            {% endfor %}
          </ul>
        </div>
      </div>

      <!-- Nutrition info if available -->
      {% if nutrition.nutrition %}
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-secondary text-white">
          <h4 class="mb-0">Nutrition Facts</h4>
        </div>
        <div class="card-body">
          <div class="row">
            {% for name, data in nutrition.nutrition.items %}
            {% if forloop.counter <= 6 and name != "calories" and name != "Calories" %}
            <div class="col-6 mb-3">
              <div class="d-flex align-items-center">
                <div class="nutrition-icon bg-light rounded-circle p-2 me-2">
                  <i class="bi bi-circle-fill text-secondary"></i>
                </div>
                <div>
                  <h6 class="mb-0">{{ name|title }}</h6>
                  <p class="mb-0 small text-muted">
                    {% if data.amount %}{{ data.amount }} {{ data.unit }}
                    {% else %}{{ data }}
                    {% endif %}
                  </p>
                </div>
              </div>
          </div>
          {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Instructions -->
  <div class="col-12 mt-4">
    <div class="card border-0" style="box-shadow: var(--shadow-md); border-radius: var(--radius-lg);">
      <div class="card-header text-white" style="background: var(--primary-color); border-bottom: none; padding: var(--space-4); border-radius: var(--radius-lg) var(--radius-lg) 0 0;">
        <h4 class="mb-0">Instructions</h4>
      </div>
      <div class="card-body" style="padding: var(--space-4);">
        <ol class="list-group list-group-flush">
          {% for instruction in recipe.instructions %}
          <li class="list-group-item border-0 d-flex" style="padding: var(--space-3) 0; background: var(--surface); border-radius: var(--radius-md); margin-bottom: var(--space-2); padding-left: var(--space-3); border-left: 4px solid var(--primary-color);">
            <span
              class="instruction-number text-white rounded-circle me-3 d-flex align-items-center justify-content-center"
              style="min-width: 32px; height: 32px; background: var(--primary-color); font-weight: var(--font-weight-semibold);">{{ forloop.counter }}</span>
            <div style="color: var(--text-primary); line-height: 1.5;">{{ instruction }}</div>
          </li>
          {% empty %}
          <li class="list-group-item border-0" style="color: var(--text-secondary);">No instructions available</li>
          {% endfor %}
        </ol>
      </div>
    </div>
  </div>
</div>
</div>
{% endblock %}

{% block extra_css %}
<!-- All CSS is now consolidated in base.css, components.css, and pages.css -->
{% endblock %}