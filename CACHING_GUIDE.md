# Recipe Finder Caching System Guide

## Overview

The Recipe Finder application now implements a comprehensive 1-day caching strategy to balance API cost savings and data freshness. This guide explains the caching system and how to manage it.

## Cache Duration Settings

### Recipe Data Cache: 1 Day (86,400 seconds)
- **Recipe search results** (all search types: name, cuisine, diet, time)
- **Recipe details** from all APIs (Tasty, Spoonacular, TheMealDB)
- **Nutrition information**
- **Random recipes** (upgraded from 30 minutes to 1 day)

### API Rate Limiting Cache: 24 Hours (86,400 seconds)
- **Tasty API request counting** (preserved for proper rate limiting)

## Cache Key Generation

The system now uses consistent cache key generation with sorted JSON serialization:

```python
# Before (inconsistent)
cache_key = f"search_recipes:{query}:{str(filters)}"

# After (consistent)
cache_key = _generate_cache_key("search_recipes", query, filters)
```

This prevents cache key collisions caused by different filter ordering.

## Management Commands

### 1. Manual Cache Clearing

Clear recipe cache manually when needed:

```bash
# Clear all recipe cache
python manage.py clear_recipe_cache --confirm

# Show cache statistics before clearing
python manage.py clear_recipe_cache --stats

# Interactive mode (asks for confirmation)
python manage.py clear_recipe_cache
```

### 2. Automatic Monthly Cache Clearing

Automatically clear cache on the last day of each month:

```bash
# Run automatic monthly clearing
python manage.py auto_clear_monthly_cache

# Force clearing regardless of date
python manage.py auto_clear_monthly_cache --force

# Dry run (show what would be cleared)
python manage.py auto_clear_monthly_cache --dry-run
```

### 3. Cache Statistics

Monitor cache usage and performance:

```bash
# Basic cache statistics
python manage.py cache_stats

# Detailed statistics with file information
python manage.py cache_stats --detailed
```

## Scheduling Automatic Cache Clearing

### Linux/macOS (Cron)

Add to crontab (`crontab -e`):

```bash
# Clear cache on last day of month at midnight
0 0 28-31 * * [ $(date -d tomorrow +\%d) -eq 1 ] && cd /path/to/recipe_finder && python manage.py auto_clear_monthly_cache
```

### Windows (Task Scheduler)

1. Open Task Scheduler
2. Create Basic Task
3. Set trigger: Monthly, last day of month, 12:00 AM
4. Set action: Start a program
5. Program: `python`
6. Arguments: `manage.py auto_clear_monthly_cache`
7. Start in: `/path/to/recipe_finder`

### Alternative: Django-Crontab

Install and configure django-crontab:

```bash
pip install django-crontab
```

Add to `settings.py`:

```python
INSTALLED_APPS = [
    # ... other apps
    'django_crontab',
]

CRONJOBS = [
    ('0 0 28-31 * *', 'recipes.management.commands.auto_clear_monthly_cache.Command', 
     '>> /tmp/cache_clear.log 2>&1'),
]
```

## Cache Benefits

### API Cost Savings
- **Tasty API**: 10 requests/day limit → 1 day of free repeated searches
- **Spoonacular API**: Paid service → Significant cost reduction
- **TheMealDB**: Free but rate-limited → Better performance

### Performance Improvements
- **First search**: Normal API response time
- **Repeated searches**: Instant response (cache hit)
- **Popular searches**: Benefit multiple users

### Example Savings
If 100 users search for "chicken recipes" in 1 day:
- **Without cache**: 100 API calls
- **With 1-day cache**: 1 API call + 99 cache hits

## Cache Storage

### File-Based Cache
- **Location**: `django_cache/` directory
- **Format**: Django's file-based cache backend
- **Persistence**: Survives server restarts
- **Sharing**: Shared across all users

### Cache File Management
- Files are automatically created/updated
- Old files are overwritten when cache expires
- Manual clearing removes all cache files

## Monitoring and Maintenance

### Regular Monitoring
```bash
# Check cache size and file count
python manage.py cache_stats

# Monitor cache effectiveness
python manage.py cache_stats --detailed
```

### Emergency Cache Clearing
```bash
# Clear cache immediately if needed
python manage.py clear_recipe_cache --confirm
```

### Cache Health Indicators
- **File count**: Should grow over time, reset monthly
- **Total size**: Monitor for disk space usage
- **File ages**: Should show recent activity

## Troubleshooting

### Cache Not Working
1. Check Django cache configuration in `settings.py`
2. Verify `django_cache/` directory exists and is writable
3. Check for import errors in `api_manager.py`

### Cache Too Large
1. Run `python manage.py cache_stats` to check size
2. Clear cache manually if needed
3. Consider reducing cache duration if necessary

### Stale Data Issues
1. Clear cache manually: `python manage.py clear_recipe_cache --confirm`
2. Check if automatic monthly clearing is working
3. Verify cache duration settings

## Configuration

### Changing Cache Duration

Edit `recipes/api_manager.py`:

```python
# Cache duration constants (in seconds)
RECIPE_CACHE_DURATION = 86400  # 1 day
API_RATE_LIMIT_CACHE_DURATION = 86400  # 24 hours
```

### Cache Backend Configuration

Edit `recipe_finder/settings.py`:

```python
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': os.path.join(BASE_DIR, 'django_cache'),
        'OPTIONS': {
            'MAX_ENTRIES': 1000,  # Optional: limit cache entries
        }
    }
}
```

## Best Practices

1. **Monitor cache size** regularly with `cache_stats`
2. **Schedule automatic clearing** for maintenance
3. **Clear cache manually** when APIs change significantly
4. **Test cache effectiveness** by checking response times
5. **Keep backups** of important cache data if needed

## Future Enhancements

Potential improvements for the caching system:

1. **Redis cache backend** for better performance
2. **Cache versioning** for easier invalidation
3. **Selective cache clearing** by pattern
4. **Cache warming** for popular searches
5. **Cache hit/miss metrics** for monitoring
