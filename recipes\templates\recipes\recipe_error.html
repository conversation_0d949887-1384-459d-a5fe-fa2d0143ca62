{% extends 'recipes/base.html' %}
{% load static %}

{% block title %}Recipe Not Found - Recipe finder{% endblock %}

{% block content %}
<div class="container my-5">
  <div class="error-container bg-neutral p-5 rounded shadow-lg text-center">
    <div class="mb-4">
      <i class="bi bi-exclamation-triangle text-accent" style="font-size: 4rem;"></i>
    </div>
    <h1 class="mb-4">Recipe Not Available</h1>
    <p class="lead mb-4">We're sorry, but we couldn't find the recipe you're looking for.</p>
    <p class="text-muted mb-4">{{ error }}</p>
    <div class="d-flex justify-content-center gap-3">
      <a href="javascript:history.back()" class="btn btn-outline-primary">
        <i class="bi bi-arrow-left"></i> Go Back
      </a>
      <a href="{% url 'recipes:home' %}" class="btn btn-primary">
        <i class="bi bi-house"></i> Home
      </a>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  /* Custom styles using brand colors */
  .bg-neutral {
    background-color: #FFF8E1 !important;
  }

  .text-accent {
    color: #C62828 !important;
  }

  .btn-primary {
    background-color: #FF6B35;
    border-color: #FF6B35;
  }

  .btn-primary:hover {
    background-color: #e55a24;
    border-color: #e55a24;
  }

  .btn-outline-primary {
    color: #FF6B35;
    border-color: #FF6B35;
  }

  .btn-outline-primary:hover {
    background-color: #FF6B35;
    color: white;
  }

  .error-container {
    max-width: 700px;
    margin: 0 auto;
    border-left: 5px solid #C62828;
  }
</style>
{% endblock %}