@echo off
REM Batch script to clear recipe cache monthly
REM This script should be scheduled to run on the last day of each month

echo Starting monthly cache clearing...
echo Date: %date% %time%

REM Change to the project directory
cd /d "C:\Users\<USER>\Desktop\MVPs\recipe_finder"

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
)

REM Run the cache clearing command
echo Running cache clearing command...
python manage.py auto_clear_monthly_cache --force

REM Log the result
if %errorlevel% equ 0 (
    echo Cache clearing completed successfully at %date% %time%
) else (
    echo Cache clearing failed with error code %errorlevel% at %date% %time%
)

echo Monthly cache clearing finished.
pause
