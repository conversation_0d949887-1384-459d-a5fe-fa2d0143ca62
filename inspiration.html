<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recipe Finder</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Section */
        .header {
            text-align: center;
            padding: 40px 0;
            background: white;
            border-bottom: 1px solid #e9ecef;
        }

        .logo {
            color: #ff6b35;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 20px;
            letter-spacing: -0.5px;
        }

        .search-title {
            color: #ff6b35;
            font-size: 48px;
            font-weight: 300;
            margin-bottom: 12px;
            letter-spacing: -1px;
        }

        .search-subtitle {
            color: #6c757d;
            font-size: 18px;
            margin-bottom: 40px;
        }

        /* Search Section */
        .search-container {
            max-width: 800px;
            margin: 0 auto 30px;
        }

        .search-box {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 16px 60px 16px 20px;
            font-size: 16px;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            outline: none;
            transition: all 0.3s ease;
            background: white;
        }

        .search-input:focus {
            border-color: #ff6b35;
            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: #ff6b35;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .search-btn:hover {
            background: #e55a2b;
        }

        .search-btn svg {
            width: 20px;
            height: 20px;
            fill: white;
        }

        /* Filter Pills */
        .filter-pills {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .pill {
            padding: 12px 24px;
            border-radius: 25px;
            border: 2px solid #e9ecef;
            background: white;
            color: #6c757d;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .pill.active {
            background: #ff6b35;
            border-color: #ff6b35;
            color: white;
        }

        .pill:hover:not(.active) {
            border-color: #ff6b35;
            color: #ff6b35;
        }

        /* Results Section */
        .results-section {
            padding: 60px 0;
            opacity: 0;
            transform: translateY(20px);
            animation: slideUp 0.6s ease forwards;
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #212529;
        }

        .view-all {
            color: #ff6b35;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .view-all:hover {
            color: #e55a2b;
        }

        /* Horizontal Carousel */
        .carousel-container {
            position: relative;
            margin-bottom: 50px;
        }

        .carousel {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding: 20px 0;
            scroll-behavior: smooth;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .carousel::-webkit-scrollbar {
            display: none;
        }

        .recipe-card {
            min-width: 280px;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .recipe-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .recipe-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
            background: linear-gradient(45deg, #ff6b35, #ff8c69);
        }

        .recipe-content {
            padding: 20px;
        }

        .recipe-title {
            font-size: 18px;
            font-weight: 600;
            color: #212529;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .recipe-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .recipe-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .tag {
            background: #fff3f0;
            color: #ff6b35;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        /* Vertical Grid Section */
        .grid-section {
            margin-bottom: 50px;
        }

        .recipe-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
            margin-top: 24px;
        }

        .grid-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .grid-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .grid-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: linear-gradient(45deg, #ff6b35, #ff8c69, #ffa07a);
        }

        /* Carousel Navigation */
        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            border: none;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            z-index: 2;
        }

        .carousel-nav:hover {
            background: #ff6b35;
            color: white;
        }

        .carousel-nav.prev {
            left: -20px;
        }

        .carousel-nav.next {
            right: -20px;
        }

        .carousel-nav svg {
            width: 20px;
            height: 20px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .search-title {
                font-size: 36px;
            }
            
            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            
            .carousel-nav {
                display: none;
            }
            
            .recipe-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-pills {
                gap: 8px;
            }
            
            .pill {
                padding: 10px 16px;
                font-size: 13px;
            }
        }

        /* Loading States */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="results-section" id="resultsSection">
            <!-- Trending Recipes Carousel -->
            <div class="carousel-container">
                <div class="section-header">
                    <h2 class="section-title">🔥 Trending This Week</h2>
                    <a href="#" class="view-all">View all</a>
                </div>
                <div class="carousel" id="trendingCarousel">
                    <div class="recipe-card">
                        <div class="recipe-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Spicy Jollof Rice</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 45 min</span>
                                <span>👥 4 servings</span>
                                <span>⭐ 4.8</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Nigerian</span>
                                <span class="tag">Main Dish</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recipe-card">
                        <div class="recipe-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Creamy Egusi Soup</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 60 min</span>
                                <span>👥 6 servings</span>
                                <span>⭐ 4.9</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Nigerian</span>
                                <span class="tag">Soup</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recipe-card">
                        <div class="recipe-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Chicken Suya Skewers</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 30 min</span>
                                <span>👥 4 servings</span>
                                <span>⭐ 4.7</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Grilled</span>
                                <span class="tag">Spicy</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recipe-card">
                        <div class="recipe-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Plantain Pancakes</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 20 min</span>
                                <span>👥 2 servings</span>
                                <span>⭐ 4.6</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Breakfast</span>
                                <span class="tag">Sweet</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recipe-card">
                        <div class="recipe-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Pepper Soup</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 40 min</span>
                                <span>👥 4 servings</span>
                                <span>⭐ 4.5</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Spicy</span>
                                <span class="tag">Comfort</span>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="carousel-nav prev" onclick="scrollCarousel('trendingCarousel', -300)">
                    <svg viewBox="0 0 24 24"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>
                </button>
                <button class="carousel-nav next" onclick="scrollCarousel('trendingCarousel', 300)">
                    <svg viewBox="0 0 24 24"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/></svg>
                </button>
            </div>

            <!-- Quick & Easy Carousel -->
            <div class="carousel-container">
                <div class="section-header">
                    <h2 class="section-title">⚡ Quick & Easy (Under 30 min)</h2>
                    <a href="#" class="view-all">View all</a>
                </div>
                <div class="carousel" id="quickCarousel">
                    <div class="recipe-card">
                        <div class="recipe-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">15-Minute Stir Fry</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 15 min</span>
                                <span>👥 2 servings</span>
                                <span>⭐ 4.4</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Quick</span>
                                <span class="tag">Healthy</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recipe-card">
                        <div class="recipe-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Instant Noodle Upgrade</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 10 min</span>
                                <span>👥 1 serving</span>
                                <span>⭐ 4.2</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Student</span>
                                <span class="tag">Budget</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recipe-card">
                        <div class="recipe-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Avocado Toast Plus</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 8 min</span>
                                <span>👥 1 serving</span>
                                <span>⭐ 4.3</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Breakfast</span>
                                <span class="tag">Healthy</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recipe-card">
                        <div class="recipe-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Microwave Mug Cake</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 5 min</span>
                                <span>👥 1 serving</span>
                                <span>⭐ 4.1</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Dessert</span>
                                <span class="tag">Single</span>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="carousel-nav prev" onclick="scrollCarousel('quickCarousel', -300)">
                    <svg viewBox="0 0 24 24"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>
                </button>
                <button class="carousel-nav next" onclick="scrollCarousel('quickCarousel', 300)">
                    <svg viewBox="0 0 24 24"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/></svg>
                </button>
            </div>

            <!-- All Results Grid -->
            <div class="grid-section">
                <div class="section-header">
                    <h2 class="section-title">All Recipes</h2>
                    <span style="color: #6c757d; font-size: 14px;">Showing 1-12 of 847 results</span>
                </div>
                <div class="recipe-grid">
                    <div class="grid-card">
                        <div class="grid-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Traditional Amala & Ewedu</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 50 min</span>
                                <span>👥 4 servings</span>
                                <span>⭐ 4.7</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Traditional</span>
                                <span class="tag">Nigerian</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid-card">
                        <div class="grid-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Coconut Rice Delight</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 35 min</span>
                                <span>👥 6 servings</span>
                                <span>⭐ 4.6</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Coconut</span>
                                <span class="tag">Festive</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid-card">
                        <div class="grid-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Spiced Fish Stew</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 45 min</span>
                                <span>👥 4 servings</span>
                                <span>⭐ 4.8</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Seafood</span>
                                <span class="tag">Spicy</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid-card">
                        <div class="grid-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Yam Pottage (Asaro)</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 40 min</span>
                                <span>👥 5 servings</span>
                                <span>⭐ 4.5</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Comfort</span>
                                <span class="tag">Hearty</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid-card">
                        <div class="grid-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Meat Pie Perfection</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 90 min</span>
                                <span>👥 8 servings</span>
                                <span>⭐ 4.9</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Pastry</span>
                                <span class="tag">Party</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid-card">
                        <div class="grid-image"></div>
                        <div class="recipe-content">
                            <h3 class="recipe-title">Chin Chin Crunch</h3>
                            <div class="recipe-meta">
                                <span>⏱️ 60 min</span>
                                <span>👥 10 servings</span>
                                <span>⭐ 4.4</span>
                            </div>
                            <div class="recipe-tags">
                                <span class="tag">Snack</span>
                                <span class="tag">Crispy</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function performSearch() {
            const searchInput = document.getElementById('searchInput');
            const query = searchInput.value.trim();
            
            if (query) {
                // Simulate search - in real app, this would make an API call
                console.log('Searching for:', query);
                
                // Add some visual feedback
                const searchBtn = document.querySelector('.search-btn');
                searchBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    searchBtn.style.transform = 'scale(1)';
                }, 150);
            }
        }

        function setActiveFilter(element) {
            // Remove active class from all pills
            document.querySelectorAll('.pill').forEach(pill => {
                pill.classList.remove('active');
            });
            
            // Add active class to clicked pill
            element.classList.add('active');
            
            // In a real app, this would filter the results
            console.log('Filter changed to:', element.textContent);
        }

        function scrollCarousel(carouselId, scrollAmount) {
            const carousel = document.getElementById(carouselId);
            carousel.scrollBy({
                left: scrollAmount,
                behavior: 'smooth'
            });
        }

        // Handle enter key in search
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Auto-show results when page loads (for demo)
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('resultsSection').style.display = 'block';
            }, 500);
        });

        // Add scroll shadows to carousels
        document.querySelectorAll('.carousel').forEach(carousel => {
            carousel.addEventListener('scroll', function() {
                const container = this.parentElement;
                const scrollLeft = this.scrollLeft;
                const maxScroll = this.scrollWidth - this.clientWidth;
                
                // Update navigation button visibility
                const prevBtn = container.querySelector('.carousel-nav.prev');
                const nextBtn = container.querySelector('.carousel-nav.next');
                
                if (prevBtn && nextBtn) {
                    prevBtn.style.opacity = scrollLeft > 0 ? '1' : '0.5';
                    nextBtn.style.opacity = scrollLeft < maxScroll ? '1' : '0.5';
                }
            });
        });

        // Initialize carousel navigation visibility
        document.querySelectorAll('.carousel').forEach(carousel => {
            const container = carousel.parentElement;
            const prevBtn = container.querySelector('.carousel-nav.prev');
            if (prevBtn) {
                prevBtn.style.opacity = '0.5';
            }
        });
    </script>
</body>
</html>