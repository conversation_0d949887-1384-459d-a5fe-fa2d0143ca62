<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Bottom Navigation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF6B35;
            /* Warm orange - stimulates appetite and creates urgency */
            --secondary-color: #4CAF50;
            /* Fresh green - suggests freshness and health */
            --accent-color: #C62828;
            /* Deep red - creates excitement and appetite appeal */
            --neutral-color: #FFF8E1;
            /* Warm cream - feels homey and approachable */
            --primary-dark: #E55A2B;
            /* Darker version of primary for hover states */
            --secondary-dark: #3D8B3F;
            /* Darker version of secondary for hover states */
            --text-on-primary: #FFFFFF;
            /* Text color on primary background */
            --text-on-secondary: #FFFFFF;
            /* Text color on secondary background */
            --text-dark: #333333;
            /* Dark text for general content */
            --text-light: #666666;
            /* Lighter text for secondary content */
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, sans-serif;
            background: white;
            min-height: 200vh;
            overflow-x: hidden;
        }

        /* Top Bar */
        .topbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            z-index: 100;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .topbar.hidden {
            transform: translateY(-100%);
        }

        .logo {
            font-size: 20px;
            font-weight: bold;
            color: var(--text-dark);
        }

        /* Main Content */
        .content {
            padding: 80px 20px 100px;
            max-width: 400px;
            margin: 0 auto;
        }

        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            margin-bottom: 15px;
            font-size: 24px;
            color: var(--primary-color);
        }

        .card p {
            line-height: 1.6;
            color: var(--text-dark);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            z-index: 100;
            transition: transform 0.3s ease;
            box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
        }

        .bottom-nav.hidden {
            transform: translateY(100%);
        }

        .nav-container {
            position: relative;
            width: 100%;
            max-width: 300px;
            height: 60px;
            background: var(--neutral-color);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px 16px;
            border-radius: 20px;
            z-index: 2;
        }

        .nav-item.active {
            background: rgba(76, 175, 80, 0.1);
        }

        .nav-item .icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            transition: all 0.3s ease;
        }

        .nav-item .label {
            font-size: 12px;
            color: var(--text-light);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-item.active .label {
            color: var(--secondary-color);
        }

        /* New Button (Elevated) */
        .nav-item.new {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
            transition: all 0.3s ease;
        }

        .nav-item.new:hover {
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 12px 30px rgba(255, 107, 53, 0.5);
        }

        .nav-item.new .icon {
            color: var(--text-on-primary);
            margin-bottom: 0;
        }

        .nav-item.new .label {
            color: var(--text-on-primary);
            font-size: 10px;
            margin-top: 2px;
        }

        /* Curved cutout effect */
        .nav-container::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 40px;
            background: var(--neutral-color);
            border-radius: 0 0 40px 40px;
            z-index: 1;
        }

        .nav-container::after {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 50px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 0 0 50px 50px;
            z-index: 0;
        }

        /* Icons */
        .home-icon {
            stroke: var(--text-light);
            fill: none;
            stroke-width: 2;
        }

        .nav-item.active .home-icon {
            stroke: var(--secondary-color);
        }

        .discover-icon {
            stroke: var(--text-light);
            fill: none;
            stroke-width: 2;
        }

        .nav-item.active .discover-icon {
            stroke: var(--secondary-color);
        }

        .plus-icon {
            stroke: var(--text-on-primary);
            fill: none;
            stroke-width: 2;
        }

        /* Responsive */
        @media (max-width: 375px) {
            .nav-container {
                max-width: 280px;
            }
            
            .nav-item {
                padding: 6px 12px;
            }
        }
    </style>
</head>
<body>
    <!-- Top Bar -->
    <div class="topbar" id="topbar">
        <div class="logo">App Name</div>
    </div>

    <!-- Main Content -->
    <div class="content">
        <div class="card">
            <h2>Welcome Home</h2>
            <p>This is your home screen. Scroll down to see the navigation hide and show based on scroll direction.</p>
        </div>
        
        <div class="card">
            <h2>Discover New Content</h2>
            <p>Explore and discover amazing new features and content tailored just for you.</p>
        </div>
        
        <div class="card">
            <h2>Create Something New</h2>
            <p>Use the elevated '+' button to create new content, posts, or start new projects.</p>
        </div>
        
        <div class="card">
            <h2>Smooth Interactions</h2>
            <p>Notice how the navigation responds to your scrolling with smooth animations.</p>
        </div>
        
        <div class="card">
            <h2>Modern Design</h2>
            <p>Clean, modern interface with glassmorphism effects and thoughtful interactions.</p>
        </div>
        
        <div class="card">
            <h2>Keep Scrolling</h2>
            <p>Continue scrolling to test the hide/show behavior of both the top bar and bottom navigation.</p>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav" id="bottomNav">
        <div class="nav-container">
            <div class="nav-item active" data-page="home">
                <svg class="icon home-icon" viewBox="0 0 24 24">
                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
                <span class="label">Home</span>
            </div>
            
            <div class="nav-item new" data-page="new">
                <svg class="icon plus-icon" viewBox="0 0 24 24">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span class="label">New</span>
            </div>
            
            <div class="nav-item" data-page="discover">
                <svg class="icon discover-icon" viewBox="0 0 24 24">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                </svg>
                <span class="label">Discover</span>
            </div>
        </div>
    </div>

    <script>
        let lastScrollTop = 0;
        const topbar = document.getElementById('topbar');
        const bottomNav = document.getElementById('bottomNav');
        const navItems = document.querySelectorAll('.nav-item');
        
        // Scroll behavior for hiding/showing navigation
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > lastScrollTop && scrollTop > 60) {
                // Scrolling down
                topbar.classList.add('hidden');
                bottomNav.classList.add('hidden');
            } else if (scrollTop < lastScrollTop) {
                // Scrolling up
                topbar.classList.remove('hidden');
                bottomNav.classList.remove('hidden');
            }
            
            lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
        });
        
        // Navigation item click handlers
        navItems.forEach(item => {
            item.addEventListener('click', function() {
                // Remove active class from all items
                navItems.forEach(nav => nav.classList.remove('active'));
                
                // Add active class to clicked item (except for new button)
                if (!this.classList.contains('new')) {
                    this.classList.add('active');
                }
                
                // Handle different navigation actions
                const page = this.getAttribute('data-page');
                switch(page) {
                    case 'home':
                        console.log('Navigate to Home');
                        break;
                    case 'new':
                        console.log('Create New Content');
                        // Add a subtle bounce animation
                        this.style.transform = 'translateX(-50%) translateY(-2px) scale(0.95)';
                        setTimeout(() => {
                            this.style.transform = 'translateX(-50%) translateY(-2px) scale(1)';
                        }, 150);
                        break;
                    case 'discover':
                        console.log('Navigate to Discover');
                        break;
                }
            });
        });
        
        // Add touch feedback for mobile
        navItems.forEach(item => {
            item.addEventListener('touchstart', function() {
                this.style.transform = this.classList.contains('new') ? 
                    'translateX(-50%) translateY(-2px) scale(0.95)' : 'scale(0.95)';
            });
            
            item.addEventListener('touchend', function() {
                this.style.transform = this.classList.contains('new') ? 
                    'translateX(-50%) translateY(-2px) scale(1)' : 'scale(1)';
            });
        });
    </script>
</body>
</html>