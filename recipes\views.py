from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from .api_manager import search_recipes as api_search_recipes
from .api_manager import get_recipe_details as api_get_recipe_details
from .api_manager import get_nutrition_info
from .api_manager import _spoonacular_search_recipes
from .api_manager import unified_search
from .discover_api import (
    get_random_recipe, get_recipes_by_category, get_recipes_by_area,
    get_all_categories, get_all_areas, get_discover_page_data
)
import logging

logger = logging.getLogger(__name__)

def recipe_detail(request, recipe_id):
    """View for displaying a single recipe's details (API only)"""
    logger.debug(f"recipe_detail called with recipe_id: {recipe_id}")

    # Only handle API recipes (format: 'source:id')
    if isinstance(recipe_id, str) and ':' in recipe_id:
        source, api_id = recipe_id.split(':', 1)
        logger.debug(f"Parsed recipe_id - source: {source}, api_id: {api_id}")

        try:
            recipe = api_get_recipe_details(api_id, source)
            logger.debug(f"Retrieved recipe: {recipe.get('title', 'Unknown')} from {source}")

            nutrition = {}
            if recipe.get('ingredients'):
                try:
                    nutrition = get_nutrition_info(recipe['ingredients'])
                    logger.debug(f"Retrieved nutrition data: {len(nutrition.get('nutrition', {}))} nutrients")
                except Exception as e:
                    logger.warning(f"Failed to get nutrition info: {e}")

            context = {
                'recipe': recipe,
                'nutrition': nutrition,
                'is_api_recipe': True,
            }
            return render(request, 'recipes/recipe_detail_api.html', context)
        except Exception as e:
            logger.error(f"Error retrieving recipe details for {recipe_id}: {e}")
            context = {
                'error': str(e),
                'recipe_id': recipe_id,
            }
            return render(request, 'recipes/recipe_error.html', context)
    else:
        # If not an API recipe, show error
        context = {
            'error': f'Invalid or unsupported recipe ID: {recipe_id}',
            'recipe_id': recipe_id,
        }
        return render(request, 'recipes/recipe_error.html', context)

def search_by_ingredients(request):
    """
    Search for recipes using the API manager (Tasty first, Spoonacular fallback).
    """
    if request.method == 'GET':
        # Get cuisine filter
        cuisine = request.GET.get('cuisine', 'african')  # Default to African
        
        # --- Free-text ingredient search ---
        if 'ingredient_text' in request.GET:
            ingredient_text = request.GET.get('ingredient_text', '').strip()
            if ingredient_text:
                try:
                    api_results = api_search_recipes(ingredient_text, {'cuisine': cuisine})
                    logger.debug(f"search_by_ingredients: Found {len(api_results)} API recipes for query '{ingredient_text}' with cuisine '{cuisine}'.")
                    if api_results:
                        logger.debug(f"First recipe: {api_results[0]}")
                        # Filter out recipes missing source or id and create combined_id
                        valid_api_results = []
                        for recipe in api_results:
                            if recipe.get('source') and recipe.get('id'):
                                recipe['combined_id'] = f"{recipe['source']}:{recipe['id']}"
                                valid_api_results.append(recipe)
                        context = {
                            'api_recipes': valid_api_results,
                            'search_term': ingredient_text,
                            'use_api': True,
                            'selected_cuisine': cuisine,
                        }
                        return render(request, 'recipes/partials/recipe_results_api.html', context)
                    else:
                        # No results from API, show empty state with search term
                        context = {
                            'api_recipes': [],
                            'search_term': ingredient_text,
                            'use_api': True,
                            'selected_cuisine': cuisine,
                            'error': 'No recipes found with those ingredients.'
                        }
                        return render(request, 'recipes/partials/recipe_results_api.html', context)
                except Exception as e:
                    logger.error(f"API search failed: {str(e)}", exc_info=True)
                    # API failed, show error state in the API template
                    context = {
                        'api_recipes': [],
                        'search_term': ingredient_text,
                        'use_api': True,
                        'selected_cuisine': cuisine,
                        'error': f"Failed to load recipes. Please try again later."
                    }
                    return render(request, 'recipes/partials/recipe_results_api.html', context)

        # Note: Ingredient ID search removed - only text-based ingredient search is supported

    # Default context for initial page load
    context = {
        'api_recipes': [],
        'use_api': True,
        'selected_cuisine': 'african',
        'error': 'Enter an ingredient to search for recipes.'
    }
    # If this is an HTMX request, return only the results partial
    if request.headers.get('HX-Request'):
        return render(request, 'recipes/partials/recipe_results_api.html', context)
    # Otherwise return the full search page
    return render(request, 'recipes/search.html', context)

# ingredient_autocomplete function removed - no longer needed without database models

@require_http_methods(["GET"])
def api_search(request):
    """Search for recipes using the API manager"""
    query = request.GET.get('q', '').strip()
    if not query:
        return JsonResponse({'success': False, 'error': 'No search query provided'})
    
    try:
        # Get optional filters from request
        filters = {}
        for key in request.GET.keys():
            if key != 'q' and request.GET.get(key):
                filters[key] = request.GET.get(key)
        
        results = api_search_recipes(query, filters)
        return JsonResponse({
            'success': True, 
            'results': results,
            'count': len(results)
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["GET"])
def api_recipe_detail(request, recipe_id, source='tasty'):
    """Get recipe details using the API manager"""
    try:
        recipe = api_get_recipe_details(recipe_id, source)
        return JsonResponse({
            'success': True,
            'recipe': recipe
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

def spoonacular_search_by_name(request):
    """
    Search for recipes by name using Spoonacular only (no fallback, no filters).
    Returns JSON results.
    """
    if request.method != 'GET':
        return JsonResponse({'success': False, 'error': 'GET only'}, status=405)
    name = request.GET.get('name', '').strip()
    if not name:
        return JsonResponse({'success': False, 'error': 'No recipe name provided'}, status=400)
    try:
        results = _spoonacular_search_recipes(name, filters=None)
        return JsonResponse({'success': True, 'results': results, 'count': len(results)})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


def unified_recipe_search(request):
    """
    Unified search view that handles all search types: name, ingredients, cuisine, dietary, time.
    Uses HTMX for dynamic content loading with URL updates.
    Supports quick filters for refined search results.
    """
    if request.method == 'GET':
        # Get search parameters
        search_type = request.GET.get('search_type', 'ingredients').strip()
        search_query = request.GET.get('search_query', '').strip()
        cuisine = request.GET.get('cuisine', '')  # Optional cuisine filter
        restrict_ingredients = request.GET.get('restrict_ingredients') == 'true'  # Ingredients restriction option
        quick_filter = request.GET.get('quick_filter', '').strip()  # Quick filter parameter

        # Validate search type
        valid_search_types = ['name', 'ingredients', 'cuisine', 'dietary', 'time', 'meal_type', 'skill_level', 'videos']
        if search_type not in valid_search_types:
            search_type = 'ingredients'  # Default to ingredients search

        # If no search query provided, show empty state
        if not search_query or search_query.isspace():
            search_type_names = {
                'name': 'recipe name',
                'ingredients': 'ingredients',
                'cuisine': 'cuisine',
                'dietary': 'dietary restrictions',
                'time': 'cooking time',
                'meal_type': 'meal type',
                'skill_level': 'skill level',
                'videos': 'search term for video recipes'
            }
            search_type_display = search_type_names.get(search_type, search_type)

            context = {
                'api_recipes': [],
                'search_term': '',
                'search_type': search_type,
                'use_api': True,
                'error': f'Please enter a {search_type_display} to search for recipes.'
            }
            if request.headers.get('HX-Request'):
                return render(request, 'recipes/partials/recipe_results_api.html', context)
            return render(request, 'recipes/search.html', context)

        try:
            # Additional validation for specific search types
            if search_type == 'time':
                from .api_manager import _parse_time_input
                parsed_time = _parse_time_input(search_query)
                if parsed_time is None:
                    context = {
                        'api_recipes': [],
                        'search_term': search_query,
                        'search_type': search_type,
                        'use_api': True,
                        'selected_cuisine': cuisine,
                        'error': f'Could not understand the time format "{search_query}". Try formats like "30 minutes", "1 hour", or "quick".'
                    }
                    if request.headers.get('HX-Request'):
                        return render(request, 'recipes/partials/recipe_results_api.html', context)
                    return render(request, 'recipes/search.html', context)

            elif search_type == 'meal_type':
                # Validate meal type input
                valid_meal_types = [
                    'breakfast', 'lunch', 'dinner', 'snack', 'dessert', 'appetizer',
                    'main course', 'side dish', 'salad', 'soup', 'beverage', 'drink',
                    'bread', 'sauce', 'marinade', 'fingerfood'
                ]
                if search_query.lower().strip() not in valid_meal_types:
                    context = {
                        'api_recipes': [],
                        'search_term': search_query,
                        'search_type': search_type,
                        'use_api': True,
                        'selected_cuisine': cuisine,
                        'error': f'Please enter a valid meal type. Try: breakfast, lunch, dinner, snack, dessert, appetizer, etc.'
                    }
                    if request.headers.get('HX-Request'):
                        return render(request, 'recipes/partials/recipe_results_api.html', context)
                    return render(request, 'recipes/search.html', context)

            elif search_type == 'skill_level':
                # Validate skill level input
                valid_skill_levels = [
                    'beginner', 'easy', 'simple', 'quick', 'intermediate', 'medium',
                    'moderate', 'advanced', 'expert', 'hard', 'difficult', 'complex'
                ]
                if search_query.lower().strip() not in valid_skill_levels:
                    context = {
                        'api_recipes': [],
                        'search_term': search_query,
                        'search_type': search_type,
                        'use_api': True,
                        'selected_cuisine': cuisine,
                        'error': f'Please enter a valid skill level: beginner, intermediate, or advanced.'
                    }
                    if request.headers.get('HX-Request'):
                        return render(request, 'recipes/partials/recipe_results_api.html', context)
                    return render(request, 'recipes/search.html', context)

            # Prepare filters
            filters = {}
            if cuisine:
                filters['cuisine'] = cuisine
            if search_type == 'ingredients' and restrict_ingredients:
                filters['restrict_ingredients'] = True
            if quick_filter and quick_filter != 'All':
                filters['quick_filter'] = quick_filter

            # Call unified search for featured recipes
            api_results = unified_search(search_type, search_query, filters)
            logger.debug(f"unified_recipe_search: Found {len(api_results)} API recipes for {search_type} search '{search_query}'.")

            # Create combined_id for all recipes and filter out invalid ones
            valid_api_results = []
            for recipe in api_results:
                # Ensure all recipes have combined_id
                if recipe.get('source') and recipe.get('id'):
                    recipe['combined_id'] = f"{recipe['source']}:{recipe['id']}"
                    valid_api_results.append(recipe)
                else:
                    # Log missing data for debugging
                    logger.warning(f"Recipe missing source or id: {recipe.get('title', 'Unknown')} - source: {recipe.get('source')}, id: {recipe.get('id')}")

            # Get additional Spoonacular results for masonry grid
            from .api_manager import get_additional_spoonacular_results
            additional_results = get_additional_spoonacular_results(search_query, search_type, valid_api_results, limit=25)

            # Add combined_id to additional results
            for recipe in additional_results:
                if recipe.get('source') and recipe.get('id'):
                    recipe['combined_id'] = f"{recipe['source']}:{recipe['id']}"

            # Select a featured recipe for detailed display
            featured_recipe = None
            featured_recipe_details = None
            featured_recipe_nutrition = None

            if valid_api_results:
                # Select the first recipe as the featured recipe
                featured_recipe = valid_api_results[0]
                logger.debug(f"Selected featured recipe: {featured_recipe.get('title')} from {featured_recipe.get('source')}")
                logger.debug(f"Featured recipe basic data: {featured_recipe}")

                # Start with the basic recipe info
                featured_recipe_details = featured_recipe.copy()

                # Try to get more detailed information if the source supports it
                if featured_recipe['source'] in ['spoonacular', 'tasty']:
                    try:
                        detailed_info = api_get_recipe_details(featured_recipe['id'], featured_recipe['source'])
                        if detailed_info and detailed_info.get('ingredients'):
                            # Merge detailed info with basic info, preferring detailed data
                            featured_recipe_details.update(detailed_info)
                            logger.debug(f"Successfully fetched detailed info for {featured_recipe['source']} recipe")
                        else:
                            logger.warning(f"Detailed fetch returned no data for {featured_recipe['source']} recipe, using search result data")
                    except Exception as e:
                        logger.warning(f"Failed to get detailed info for {featured_recipe['source']} recipe: {e}, using search result data")

                # Log what data we have
                logger.debug(f"Featured recipe final data - Title: {featured_recipe_details.get('title')}")
                logger.debug(f"Featured recipe ingredients count: {len(featured_recipe_details.get('ingredients', []))}")
                logger.debug(f"Featured recipe instructions count: {len(featured_recipe_details.get('instructions', []))}")
                logger.debug(f"Featured recipe source: {featured_recipe_details.get('source')}")

                # Skip nutrition for now to focus on ingredients and instructions
                featured_recipe_nutrition = {"nutrition": {}, "source": "disabled"}

            # Combine featured and additional results
            all_recipes = valid_api_results + additional_results
            logger.debug(f"Total recipes (featured + additional): {len(all_recipes)}")



            context = {
                'api_recipes': valid_api_results,  # Featured recipes for carousel
                'all_recipes': all_recipes,       # All recipes for masonry grid (legacy)
                'additional_recipes': additional_results,  # Additional recipes only
                'featured_recipe': featured_recipe,  # Basic featured recipe info
                'featured_recipe_details': featured_recipe_details,  # Detailed featured recipe info
                'featured_recipe_nutrition': featured_recipe_nutrition,  # Nutrition info for featured recipe
                'search_term': search_query,
                'search_type': search_type,
                'use_api': True,
                'selected_cuisine': cuisine,
                'quick_filter': quick_filter,  # Current quick filter
                'total_recipes_count': len(all_recipes),  # Total count for header
            }

            if not valid_api_results:
                context['error'] = f'No recipes found for {search_type} search: "{search_query}"'

            if request.headers.get('HX-Request'):
                # Construct the URL with search parameters for HTMX to push to browser history
                from urllib.parse import urlencode
                from django.urls import reverse

                search_params = {
                    'search_type': search_type,
                    'search_query': search_query,
                }
                if cuisine:
                    search_params['cuisine'] = cuisine
                if search_type == 'ingredients' and restrict_ingredients:
                    search_params['restrict_ingredients'] = 'true'
                if quick_filter and quick_filter != 'All':
                    search_params['quick_filter'] = quick_filter

                search_url = f"{reverse('recipes:unified_search')}?{urlencode(search_params)}"

                response = render(request, 'recipes/partials/recipe_results_api.html', context)
                response['HX-Push-Url'] = search_url
                return response
            return render(request, 'recipes/search.html', context)

        except Exception as e:
            logger.error(f"Unified search failed for {search_type} search '{search_query}': {str(e)}", exc_info=True)
            context = {
                'api_recipes': [],
                'search_term': search_query,
                'search_type': search_type,
                'use_api': True,
                'selected_cuisine': cuisine,
                'error': f"Failed to load recipes. Please try again later."
            }
            if request.headers.get('HX-Request'):
                # Construct the URL with search parameters for HTMX to push to browser history
                from urllib.parse import urlencode
                from django.urls import reverse

                search_params = {
                    'search_type': search_type,
                    'search_query': search_query,
                }
                if cuisine:
                    search_params['cuisine'] = cuisine
                if search_type == 'ingredients' and restrict_ingredients:
                    search_params['restrict_ingredients'] = 'true'

                search_url = f"{reverse('recipes:unified_search')}?{urlencode(search_params)}"

                response = render(request, 'recipes/partials/recipe_results_api.html', context)
                response['HX-Push-Url'] = search_url
                return response
            return render(request, 'recipes/search.html', context)

    # Default context for non-GET requests
    context = {
        'api_recipes': [],
        'search_term': '',
        'search_type': 'ingredients',
        'use_api': True,
        'error': 'Invalid request method.'
    }
    if request.headers.get('HX-Request'):
        return render(request, 'recipes/partials/recipe_results_api.html', context)
    return render(request, 'recipes/search.html', context)


# Discover page views
def discover_page(request):
    """Main discover page view"""
    try:
        discover_data = get_discover_page_data()
        context = {
            'featured_recipe': discover_data.get('featured_recipe'),
            'categories': discover_data.get('categories', []),
            'areas': discover_data.get('areas', []),
            'category_recipes': discover_data.get('category_recipes', {}),
        }
        return render(request, 'recipes/discover.html', context)
    except Exception as e:
        logger.error(f"Error loading discover page: {e}")
        context = {
            'error': 'Failed to load discover page. Please try again later.',
            'featured_recipe': None,
            'categories': [],
            'areas': [],
            'category_recipes': {},
        }
        return render(request, 'recipes/discover.html', context)


@require_http_methods(["GET"])
def discover_random_recipe(request):
    """HTMX endpoint for getting a new random recipe"""
    try:
        recipe = get_random_recipe()
        if recipe:
            return render(request, 'recipes/partials/featured_recipe.html', {'featured_recipe': recipe})
        else:
            return render(request, 'recipes/partials/featured_recipe.html', {'error': 'Failed to load random recipe'})
    except Exception as e:
        logger.error(f"Error getting random recipe: {e}")
        return render(request, 'recipes/partials/featured_recipe.html', {'error': 'Failed to load random recipe'})


@require_http_methods(["GET"])
def featured_carousel_recipes(request):
    """API endpoint for featured carousel recipes with 24-hour caching"""
    try:
        from .api_manager import get_random_recipes

        # Get 15 random recipes with 24-hour caching
        recipes = get_random_recipes(count=15)

        if recipes:
            return JsonResponse({
                'success': True,
                'recipes': recipes,
                'cached_until': 'Next day'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'No recipes available',
                'recipes': []
            })
    except Exception as e:
        logger.error(f"Error getting featured carousel recipes: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Failed to load recipes',
            'recipes': []
        })


@require_http_methods(["GET"])
def discover_category_recipes(request, category):
    """HTMX endpoint for getting recipes by category"""
    try:
        recipes = get_recipes_by_category(category, 12)  # Get more for grid display
        context = {
            'recipes': recipes,
            'category': category,
        }
        return render(request, 'recipes/partials/category_recipes.html', context)
    except Exception as e:
        logger.error(f"Error getting recipes for category {category}: {e}")
        context = {
            'recipes': [],
            'category': category,
            'error': f'Failed to load {category} recipes'
        }
        return render(request, 'recipes/partials/category_recipes.html', context)


@require_http_methods(["GET"])
def discover_area_recipes(request, area):
    """HTMX endpoint for getting recipes by area/country"""
    try:
        recipes = get_recipes_by_area(area, 12)  # Get more for grid display
        context = {
            'recipes': recipes,
            'area': area,
        }
        return render(request, 'recipes/partials/area_recipes.html', context)
    except Exception as e:
        logger.error(f"Error getting recipes for area {area}: {e}")
        context = {
            'recipes': [],
            'area': area,
            'error': f'Failed to load {area} recipes'
        }
        return render(request, 'recipes/partials/area_recipes.html', context)
