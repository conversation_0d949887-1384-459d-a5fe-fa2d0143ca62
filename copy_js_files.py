import os
import shutil

# Define paths
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
NODE_MODULES = os.path.join(BASE_DIR, 'node_modules')
STATIC_JS = os.path.join(BASE_DIR, 'static', 'js')

# Create static/js directory if it doesn't exist
os.makedirs(STATIC_JS, exist_ok=True)

# Copy HTMX
htmx_src = os.path.join(NODE_MODULES, 'htmx.org', 'dist', 'htmx.min.js')
htmx_dest = os.path.join(STATIC_JS, 'htmx.min.js')

# Copy Alpine.js
alpine_src = os.path.join(NODE_MODULES, 'alpinejs', 'dist', 'cdn.min.js')
alpine_dest = os.path.join(STATIC_JS, 'alpine.min.js')

# Perform the copy operations
if os.path.exists(htmx_src):
    shutil.copy2(htmx_src, htmx_dest)
    print(f"Copied {htmx_src} to {htmx_dest}")
else:
    print(f"Warning: {htmx_src} not found")

if os.path.exists(alpine_src):
    shutil.copy2(alpine_src, alpine_dest)
    print(f"Copied {alpine_src} to {alpine_dest}")
else:
    print(f"Warning: {alpine_src} not found")

print("JavaScript files copied successfully!")
