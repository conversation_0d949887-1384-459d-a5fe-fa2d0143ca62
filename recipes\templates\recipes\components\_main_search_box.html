{% load static %}
<!-- CSS is now consolidated in base.css, components.css, and pages.css -->
    <div class="search-container mb-4" x-data="{
        mobileSearchExpanded: false,
        filter: 'ingredients',
        searchConfirmationNeeded: false,
        confirmationTimeout: null,
        hasExistingResults: false,
        get placeholder() {
            // Strategic placeholder text removal for mobile/tablet
            const isMobile = window.innerWidth <= 768;
            const isTablet = window.innerWidth > 768 && window.innerWidth <= 992;

            if (isMobile) {
                // Minimal placeholders for mobile
                switch(this.filter) {
                    case 'name': return 'Recipe name...';
                    case 'ingredients': return 'Ingredients...';
                    case 'cuisine': return 'Cuisine type...';
                    case 'dietary': return 'Dietary needs...';
                    case 'time': return 'Cooking time...';
                    default: return 'Search...';
                }
            } else if (isTablet) {
                // Shortened placeholders for tablet
                switch(this.filter) {
                    case 'name': return 'Recipe Name (e.g., Jollof rice)';
                    case 'ingredients': return 'Ingredients (e.g., rice, onions)';
                    case 'cuisine': return 'Cuisine (e.g., Nigerian)';
                    case 'dietary': return 'Restrictions (e.g., Vegan)';
                    case 'time': return 'Time (e.g., under 30 min)';
                    default: return 'Search for recipes...';
                }
            } else {
                // Full placeholders for desktop
                switch(this.filter) {
                    case 'name': return 'Search by Recipe Name (e.g., Jollof rice, Egusi soup)';
                    case 'ingredients': return 'Search by Ingredients (e.g., rice, onions, pepper)';
                    case 'cuisine': return 'Search by Cuisine (e.g., Nigerian, West African)';
                    case 'dietary': return 'Search by Restrictions (e.g., Vegan, Gluten-Free)';
                    case 'time': return 'Search by Cooking Time (e.g., under 30 minutes)';
                    default: return 'Search for recipes...';
                }
            }
        },
        checkForExistingResults() {
            const resultsContainer = document.getElementById('recipe-results');
            this.hasExistingResults = resultsContainer && resultsContainer.innerHTML.trim() !== '' &&
                                    !resultsContainer.innerHTML.includes('No recipes found');
        },
        handleSearchSubmit(event) {
            this.checkForExistingResults();

            // If there are existing results and this is the first click, show confirmation
            if (this.hasExistingResults && !this.searchConfirmationNeeded) {
                event.preventDefault();
                this.searchConfirmationNeeded = true;

                // Clear confirmation after 3 seconds
                if (this.confirmationTimeout) clearTimeout(this.confirmationTimeout);
                this.confirmationTimeout = setTimeout(() => {
                    this.searchConfirmationNeeded = false;
                }, 3000);

                return false;
            }

            // Second click or no existing results - proceed with search
            this.searchConfirmationNeeded = false;
            if (this.confirmationTimeout) clearTimeout(this.confirmationTimeout);

            // Close mobile search on submit
            this.mobileSearchExpanded = false;
            return true;
        },
        toggleMobileSearch() {
            this.mobileSearchExpanded = !this.mobileSearchExpanded;
        }
    }">


        <!-- Modern Desktop/Tablet Search Form -->
        <form
            hx-get="{% url 'recipes:unified_search' %}"
            hx-target="#recipe-results"
            hx-trigger="submit"
            hx-indicator=".htmx-indicator"
            hx-push-url="true"
            class="search-form-wrapper animate-fade-in"
            @submit="handleSearchSubmit($event)"
            x-init="checkForExistingResults()">

            <!-- Hidden input to pass search type -->
            <input type="hidden" name="search_type" x-bind:value="filter">

            <!-- Search type selector -->
            <select class="search-type-select focus-ring" x-model="filter">
                <option value="name">Recipe Name</option>
                <option value="ingredients">Ingredients</option>
                <option value="cuisine">Cuisine</option>
                <option value="dietary">Restrictions</option>
                <option value="time">Cooking Time</option>
                <option value="meal_type">Meal Type</option>
                <option value="skill_level">Skill Level</option>
                <option value="videos">Videos</option>
            </select>

            <!-- Main search input -->
            <input
                type="text"
                class="search-input focus-ring"
                name="search_query"
                id="search-query"
                x-bind:placeholder="placeholder"
                autocomplete="off"
            />

            <!-- Ingredients restriction option (only show for ingredients search) -->
            <div class="ingredients-restriction-option" x-show="filter === 'ingredients'" x-transition>
                <label class="restriction-checkbox">
                    <input type="checkbox" name="restrict_ingredients" value="true" class="checkbox-input">
                    <span class="checkbox-label">Only these ingredients</span>
                </label>
            </div>

            <!-- Search button -->
            <button type="submit" class="search-btn-primary focus-ring" id="searchBtn">
                <span x-show="!searchConfirmationNeeded">
                    <!-- Food-themed loading spinner -->
                    <span class="search-loading-icon" id="search-loading-icon">
                        <div class="search-loading-spinner"></div>
                    </span>

                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="M21 21l-4.35-4.35"/>
                    </svg>
                    <span class="desktop-text">Search</span>
                </span>
                <span x-show="searchConfirmationNeeded" x-text="'Confirm Search'"></span>
                <span class="htmx-indicator spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            </button>
        </form>

        <!-- Mobile Search Expanded Overlay -->
        <div class="mobile-search-expanded" x-show="mobileSearchExpanded" @click.self="mobileSearchExpanded = false">
            <div class="mobile-search-content">
                <form
                    hx-get="{% url 'recipes:unified_search' %}"
                    hx-target="#recipe-results"
                    hx-trigger="submit"
                    hx-indicator=".mobile-htmx-indicator"
                    hx-push-url="true"
                    class="search-wrapper"
                    @submit="handleSearchSubmit($event)">

                    <!-- Hidden input to pass search type -->
                    <input type="hidden" name="search_type" x-bind:value="filter">

                    <!-- Search type selector -->
                    <div class="search-header">
                        <select class="search-type-select" x-model="filter">
                            <option value="name">Recipe Name</option>
                            <option value="ingredients">Ingredients</option>
                            <option value="cuisine">Cuisine</option>
                            <option value="dietary">Restrictions</option>
                            <option value="time">Cooking Time</option>
                            <option value="meal_type">Meal Type</option>
                            <option value="skill_level">Skill Level</option>
                            <option value="videos">Videos</option>
                        </select>
                    </div>

                    <!-- Main search input area -->
                    <div class="search-input-container">
                        <textarea
                            class="search-input"
                            name="search_query"
                            x-bind:placeholder="placeholder"
                            rows="1"
                            autocomplete="off"
                            oninput="autoResize(this)"
                            onkeydown="handleKeydown(event)"
                        ></textarea>

                        <!-- Ingredients restriction option (only show for ingredients search) -->
                        <div class="ingredients-restriction-option mobile" x-show="filter === 'ingredients'" x-transition>
                            <label class="restriction-checkbox mobile">
                                <input type="checkbox" name="restrict_ingredients" value="true" class="checkbox-input">
                                <span class="checkbox-label">Only these ingredients</span>
                                <span class="checkbox-hint">Find recipes using only the ingredients you listed</span>
                            </label>
                        </div>

                        <div class="search-actions">
                            <button type="submit" class="search-btn">
                                <span class="search-text" x-show="!searchConfirmationNeeded">
                                    <!-- Food-themed loading spinner -->
                                    <span class="search-loading-icon" id="mobile-search-loading-icon">
                                        <div class="search-loading-spinner"></div>
                                    </span>

                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="M21 21l-4.35-4.35"/>
                                    </svg>
                                </span>
                                <span class="search-text" x-show="searchConfirmationNeeded" x-text="'Confirm'"></span>
                                <span class="mobile-htmx-indicator spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Auto-resize textarea
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // Handle Enter key to submit (Shift+Enter for new line)
        function handleKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                event.target.closest('form').dispatchEvent(new Event('submit'));
            }
        }
    </script>
