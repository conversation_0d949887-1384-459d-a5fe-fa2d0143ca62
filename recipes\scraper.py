import requests
from bs4 import BeautifulSoup
import logging
import time
import random
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

# Common browser headers to mimic a real browser
BROWSER_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Cache-Control': 'max-age=0',
}

def make_request(url: str, timeout: int = 10) -> Optional[requests.Response]:
    """
    Make a request with browser headers and rate limiting.
    Returns None if the request fails.
    """
    try:
        # Add random delay between 1-3 seconds to avoid rate limiting
        time.sleep(random.uniform(1, 3))
        
        response = requests.get(
            url,
            headers=BROWSER_HEADERS,
            timeout=timeout
        )
        response.raise_for_status()
        return response
    except Exception as e:
        logger.error(f"Request failed for {url}: {e}")
        return None

def scrape_afrifoodnetwork_recipes(limit=4):
    """Scrape latest recipes from afrifoodnetwork.com/recipes/"""
    url = "https://afrifoodnetwork.com/recipes/"
    logger.debug(f"Attempting to scrape Afrifood Network recipes from: {url}")
    
    try:
        resp = make_request(url)
        if not resp:
            logger.error("Failed to get response from Afrifood Network - no response received")
            return []
            
        if resp.status_code != 200:
            logger.error(f"Failed to get response from Afrifood Network - status code: {resp.status_code}")
            return []
            
        soup = BeautifulSoup(resp.text, "html.parser")
        
        # Find all recipe articles
        recipe_articles = soup.find_all('article', class_='post')
        logger.debug(f"Found {len(recipe_articles)} recipe articles on Afrifood Network page")
        
        if not recipe_articles:
            logger.warning("No recipe articles found on Afrifood Network page - check if selectors are still valid")
            return []
        
        recipes = []
        for idx, article in enumerate(recipe_articles[:limit]):
            logger.debug(f"Processing recipe article {idx + 1}/{min(len(recipe_articles), limit)}")
            
            try:
                # Find the recipe title and link
                title_tag = article.find('h3', class_='entry-title')
                if not title_tag:
                    logger.warning(f"Missing title tag in recipe article {idx + 1}")
                    continue
                    
                link_tag = title_tag.find('a')
                if not link_tag:
                    logger.warning(f"Missing link tag in recipe article {idx + 1}")
                    continue
                    
                recipe_url = link_tag.get('href')
                title = link_tag.get_text(strip=True)
                
                if not recipe_url:
                    logger.warning(f"Missing recipe URL in recipe article {idx + 1}")
                    continue
                
                # Find the recipe image
                img_tag = article.find('img')
                image_url = None
                if img_tag:
                    image_url = img_tag.get('src') or img_tag.get('data-src')
                    if not image_url:
                        logger.warning(f"Missing image URL in recipe article {idx + 1}")
                
                logger.debug(f"Found recipe: {title} at {recipe_url}")
                
                # Fetch ingredients from recipe page
                ingredients = []
                try:
                    logger.debug(f"Fetching ingredients for recipe: {recipe_url}")
                    detail_resp = make_request(recipe_url)
                    
                    if not detail_resp:
                        logger.warning(f"Failed to get response for recipe detail page: {recipe_url}")
                    elif detail_resp.status_code != 200:
                        logger.warning(f"Failed to get recipe detail page - status code: {detail_resp.status_code} for URL: {recipe_url}")
                    else:
                        detail_soup = BeautifulSoup(detail_resp.text, "html.parser")
                        
                        # Try different ingredient selectors
                        ing_tags = detail_soup.select('.wprm-recipe-ingredient, .ingredients li, .recipe-ingredients li, .entry-content ul li')
                        
                        if not ing_tags:
                            logger.warning(f"No ingredient tags found for recipe: {recipe_url} - check if selector is still valid")
                        else:
                            ingredients = [i.get_text(strip=True) for i in ing_tags]
                            logger.debug(f"Found {len(ingredients)} ingredients for recipe: {title}")
                            
                except Exception as e:
                    logger.error(f"Error fetching ingredients for recipe {recipe_url}: {str(e)}", exc_info=True)
                
                recipes.append({
                    'title': title,
                    'image_url': image_url,
                    'ingredients': ingredients,
                    'source_url': recipe_url
                })
                
            except Exception as e:
                logger.error(f"Error processing recipe article {idx + 1}: {str(e)}", exc_info=True)
                continue
        
        logger.debug(f"Successfully scraped {len(recipes)} recipes from Afrifood Network")
        return recipes
        
    except Exception as e:
        logger.error(f"Critical error in Afrifood Network scraper: {str(e)}", exc_info=True)
        return []

