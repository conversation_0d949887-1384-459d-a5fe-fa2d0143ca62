{% comment %}
Food-Themed Loading Animation Component
Provides multiple food-themed loading animations for recipe search functionality.
Can be used as overlay or inline loading state.
{% endcomment %}

<!-- Food Loading Overlay (Full Screen) -->
<div class="food-loader-overlay" id="food-loader-overlay" x-data="{
    loaderTheme: 'cooking',
    themes: ['cooking', 'utensils', 'ingredients', 'chef', 'mixing', 'timer', 'book', 'whisk'],
    currentThemeIndex: 0,

    rotateTheme() {
        this.currentThemeIndex = (this.currentThemeIndex + 1) % this.themes.length;
        this.loaderTheme = this.themes[this.currentThemeIndex];
    },

    init() {
        // Rotate themes every 2.5 seconds for variety
        setInterval(() => {
            this.rotateTheme();
        }, 2500);
    }
}" x-bind:class="loaderTheme + '-theme'">
    
    <div class="food-loader">
        <!-- Cooking Pot Animation -->
        <div class="cooking-pot-loader" x-show="loaderTheme === 'cooking'">
            <div class="pot">
                <div class="steam"></div>
            </div>
        </div>
        
        <!-- Spinning Utensils Animation -->
        <div class="utensils-loader" x-show="loaderTheme === 'utensils'">
            <div class="utensil" style="--rotation: 0deg;"></div>
            <div class="utensil" style="--rotation: 60deg;"></div>
            <div class="utensil" style="--rotation: 120deg;"></div>
            <div class="utensil" style="--rotation: 180deg;"></div>
            <div class="utensil" style="--rotation: 240deg;"></div>
            <div class="utensil" style="--rotation: 300deg;"></div>
        </div>
        
        <!-- Bouncing Ingredients Animation -->
        <div class="ingredients-loader" x-show="loaderTheme === 'ingredients'">
            <div class="ingredient"></div>
            <div class="ingredient"></div>
            <div class="ingredient"></div>
            <div class="ingredient"></div>
            <div class="ingredient"></div>
        </div>
        
        <!-- Chef Hat Animation -->
        <div class="chef-hat-loader" x-show="loaderTheme === 'chef'">
            <div class="chef-hat"></div>
        </div>

        <!-- Mixing Bowl Animation -->
        <div class="mixing-bowl-loader" x-show="loaderTheme === 'mixing'">
            <div class="mixing-bowl">
                <div class="mixing-ingredients">
                    <div class="ingredient-particle"></div>
                    <div class="ingredient-particle"></div>
                    <div class="ingredient-particle"></div>
                    <div class="ingredient-particle"></div>
                </div>
            </div>
        </div>

        <!-- Oven Timer Animation -->
        <div class="oven-timer-loader" x-show="loaderTheme === 'timer'">
            <div class="timer-face">
                <div class="timer-hand"></div>
                <div class="timer-numbers">
                    <span class="timer-number">12</span>
                    <span class="timer-number">3</span>
                    <span class="timer-number">6</span>
                    <span class="timer-number">9</span>
                </div>
            </div>
        </div>

        <!-- Recipe Book Animation -->
        <div class="recipe-book-loader" x-show="loaderTheme === 'book'">
            <div class="recipe-book">
                <div class="book-pages">
                    <div class="page"></div>
                    <div class="page"></div>
                    <div class="page"></div>
                </div>
            </div>
        </div>

        <!-- Whisk Animation -->
        <div class="whisk-loader" x-show="loaderTheme === 'whisk'">
            <div class="whisk"></div>
        </div>


    </div>
</div>

<!-- Inline Results Loading State -->
<div class="recipe-results-loading" id="recipe-results-loading" style="display: none;" x-data="{
    inlineTheme: 'cooking',
    inlineThemes: ['cooking', 'utensils', 'ingredients', 'chef', 'mixing', 'timer', 'book', 'whisk'],
    currentInlineIndex: 0,

    rotateInlineTheme() {
        this.currentInlineIndex = (this.currentInlineIndex + 1) % this.inlineThemes.length;
        this.inlineTheme = this.inlineThemes[this.currentInlineIndex];
    },

    init() {
        // Rotate themes every 2 seconds for inline loader
        setInterval(() => {
            this.rotateInlineTheme();
        }, 2000);
}
}" x-bind:class="inlineTheme + '-theme'">
    
    <div class="food-loader">
        <!-- Cooking Pot Animation -->
        <div class="cooking-pot-loader" x-show="inlineTheme === 'cooking'">
            <div class="pot">
                <div class="steam"></div>
            </div>
        </div>
        
        <!-- Spinning Utensils Animation -->
        <div class="utensils-loader" x-show="inlineTheme === 'utensils'">
            <div class="utensil" style="--rotation: 0deg;"></div>
            <div class="utensil" style="--rotation: 60deg;"></div>
            <div class="utensil" style="--rotation: 120deg;"></div>
            <div class="utensil" style="--rotation: 180deg;"></div>
            <div class="utensil" style="--rotation: 240deg;"></div>
            <div class="utensil" style="--rotation: 300deg;"></div>
        </div>
        
        <!-- Bouncing Ingredients Animation -->
        <div class="ingredients-loader" x-show="inlineTheme === 'ingredients'">
            <div class="ingredient"></div>
            <div class="ingredient"></div>
            <div class="ingredient"></div>
            <div class="ingredient"></div>
            <div class="ingredient"></div>
        </div>
        
        <!-- Chef Hat Animation -->
        <div class="chef-hat-loader" x-show="inlineTheme === 'chef'">
            <div class="chef-hat"></div>
        </div>

        <!-- Mixing Bowl Animation -->
        <div class="mixing-bowl-loader" x-show="inlineTheme === 'mixing'">
            <div class="mixing-bowl">
                <div class="bowl-contents"></div>
                <div class="whisk"></div>
            </div>
        </div>

        <!-- Oven Timer Animation -->
        <div class="oven-timer-loader" x-show="inlineTheme === 'timer'">
            <div class="timer-face">
                <div class="timer-hand"></div>
                <div class="timer-numbers">
                    <span class="timer-number">12</span>
                    <span class="timer-number">3</span>
                    <span class="timer-number">6</span>
                    <span class="timer-number">9</span>
                </div>
            </div>
        </div>

        <!-- Mixing Bowl Animation -->
        <div class="mixing-bowl-loader" x-show="inlineTheme === 'mixing'">
            <div class="mixing-bowl">
                <div class="mixing-ingredients">
                    <div class="ingredient-particle"></div>
                    <div class="ingredient-particle"></div>
                    <div class="ingredient-particle"></div>
                    <div class="ingredient-particle"></div>
                </div>
            </div>
        </div>

        <!-- Recipe Book Animation -->
        <div class="recipe-book-loader" x-show="inlineTheme === 'book'">
            <div class="recipe-book">
                <div class="book-pages">
                    <div class="page"></div>
                    <div class="page"></div>
                    <div class="page"></div>
                </div>
            </div>
        </div>

        <!-- Whisk Animation -->
        <div class="whisk-loader" x-show="inlineTheme === 'whisk'">
            <div class="whisk"></div>
        </div>
    </div>
</div>

<!-- Recipe Card Skeleton Loaders (for masonry grid) -->
<div class="recipe-skeletons-container" id="recipe-skeletons" style="display: none;">
    <div class="masonry-grid">
        <!-- Generate multiple skeleton cards -->
        <div class="recipe-card-skeleton">
            <div class="skeleton-image"></div>
            <div class="skeleton-title"></div>
            <div class="skeleton-meta">
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
            </div>
        </div>
        
        <div class="recipe-card-skeleton">
            <div class="skeleton-image"></div>
            <div class="skeleton-title"></div>
            <div class="skeleton-meta">
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
            </div>
        </div>
        
        <div class="recipe-card-skeleton">
            <div class="skeleton-image"></div>
            <div class="skeleton-title"></div>
            <div class="skeleton-meta">
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
            </div>
        </div>
        
        <div class="recipe-card-skeleton">
            <div class="skeleton-image"></div>
            <div class="skeleton-title"></div>
            <div class="skeleton-meta">
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
            </div>
        </div>
        
        <div class="recipe-card-skeleton">
            <div class="skeleton-image"></div>
            <div class="skeleton-title"></div>
            <div class="skeleton-meta">
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
            </div>
        </div>
        
        <div class="recipe-card-skeleton">
            <div class="skeleton-image"></div>
            <div class="skeleton-title"></div>
            <div class="skeleton-meta">
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
                <div class="skeleton-meta-item"></div>
            </div>
        </div>
    </div>
</div>

<!-- Inline Search Button Loading Spinner -->
<span class="search-loading-icon" id="search-loading-icon">
    <div class="search-loading-spinner"></div>
</span>
