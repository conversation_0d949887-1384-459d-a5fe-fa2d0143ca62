# Recipe finder App

A Django web application that allows users to find recipes based on ingredients they have available. Built with Django, HTMX, and Alpine.js for a modern, interactive experience.

## Features

- Search for recipes based on ingredients you have
- View detailed recipe instructions and ingredients
- Responsive design that works on desktop and mobile
- Interactive UI with HTMX and Alpine.js

## Tech Stack

- **Backend**: Django
- **Frontend**: HTML, CSS, JavaScript
- **Interactive UI**: HTMX, Alpine.js
- **Styling**: Bootstrap 5

## Setup Instructions

### Prerequisites

- Python 3.8+
- Node.js and npm

### Installation

1. Clone the repository

2. Create and activate a virtual environment:
   ```
   python -m venv venv
   .\venv\Scripts\activate  # Windows
   ```

3. Install Python dependencies:
   ```
   pip install django pillow
   ```

4. Install JavaScript dependencies:
   ```
   npm install htmx.org alpinejs
   ```

5. Copy JavaScript files to static directory:
   ```
   python copy_js_files.py
   ```

6. Run migrations to set up the database:
   ```
   python manage.py makemigrations
   python manage.py migrate
   ```

7. Create a superuser to access the admin panel:
   ```
   python manage.py createsuperuser
   ```

8. (Optional) Load sample data:
   ```
   python manage.py loaddata sample_data.json
   ```

9. Run the development server:
   ```
   python manage.py runserver
   ```

10. Access the application at http://127.0.0.1:8000/

## Usage

1. Browse to the homepage to see recent recipes
2. Go to "Find Recipes" to search by ingredients
3. Select ingredients you have available
4. View matching recipes sorted by relevance
5. Click on a recipe to see detailed instructions

## Admin Interface

Access the admin interface at http://127.0.0.1:8000/admin/ to:

- Add, edit, or delete recipes
- Manage ingredients
- Set up recipe-ingredient relationships

## Project Structure

- `recipes/` - Main Django app for recipe functionality
- `static/` - Static files (CSS, JavaScript, images)
- `media/` - User-uploaded files (recipe images)
- `templates/` - HTML templates

## License

MIT
