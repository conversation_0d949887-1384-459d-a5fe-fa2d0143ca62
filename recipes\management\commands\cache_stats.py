"""
Django management command to show cache statistics and information.
Usage: python manage.py cache_stats [--detailed]
"""

from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.conf import settings
import os
import glob
from datetime import datetime, timedelta
import json
from recipes.cache_utils import check_redis_health, get_cache_stats


class Command(BaseCommand):
    help = 'Show cache statistics and information'

    def add_arguments(self, parser):
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed cache information including file ages',
        )

    def handle(self, *args, **options):
        detailed = options.get('detailed')

        self.stdout.write(self.style.HTTP_INFO('=== Recipe Finder Cache Statistics ===\n'))

        # Cache configuration
        self.show_cache_config()

        # Redis health check
        self.show_redis_health()

        # Cache statistics
        self.show_cache_stats(detailed)

        # Cache duration settings
        self.show_cache_durations()

    def show_cache_config(self):
        """Show cache configuration."""
        self.stdout.write(self.style.HTTP_INFO('Cache Configuration:'))
        
        cache_config = getattr(settings, 'CACHES', {}).get('default', {})
        backend = cache_config.get('BACKEND', 'Unknown')
        location = cache_config.get('LOCATION', 'Not specified')
        
        self.stdout.write(f'  Backend: {backend}')
        self.stdout.write(f'  Location: {location}')
        
        if 'OPTIONS' in cache_config:
            self.stdout.write(f'  Options: {cache_config["OPTIONS"]}')
        
        self.stdout.write('')

    def show_redis_health(self):
        """Show Redis health status."""
        self.stdout.write(self.style.HTTP_INFO('Redis Health Status:'))

        health = check_redis_health()
        if health['redis_available']:
            self.stdout.write(self.style.SUCCESS(f'  ✓ Redis is available'))
            self.stdout.write(f'  Connection time: {health["connection_time"]}ms')

            # Show Redis statistics if available
            stats = get_cache_stats()
            if stats['redis_available']:
                self.stdout.write(f'  Memory usage: {stats["memory_usage"]}')
                self.stdout.write(f'  Connected clients: {stats["connected_clients"]}')
                if stats['hit_rate'] is not None:
                    self.stdout.write(f'  Cache hit rate: {stats["hit_rate"]}%')
        else:
            self.stdout.write(self.style.ERROR(f'  ✗ Redis is not available'))
            self.stdout.write(f'  Error: {health["error"]}')

        self.stdout.write('')

    def show_cache_stats(self, detailed=False):
        """Show cache statistics."""
        self.stdout.write(self.style.HTTP_INFO('Cache Statistics:'))
        
        cache_location = getattr(settings, 'CACHES', {}).get('default', {}).get('LOCATION')
        
        if not cache_location or not os.path.exists(cache_location):
            self.stdout.write('  Cache location not found or not accessible')
            return

        cache_files = glob.glob(os.path.join(cache_location, '*.djcache'))
        
        if not cache_files:
            self.stdout.write('  No cache files found')
            return

        # Basic statistics
        total_size = sum(os.path.getsize(f) for f in cache_files if os.path.exists(f))
        
        self.stdout.write(f'  Total cache files: {len(cache_files)}')
        self.stdout.write(f'  Total cache size: {self.format_bytes(total_size)}')
        
        # File age analysis
        now = datetime.now()
        file_ages = []
        
        for file_path in cache_files:
            if os.path.exists(file_path):
                mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                age_hours = (now - mtime).total_seconds() / 3600
                file_ages.append(age_hours)

        if file_ages:
            avg_age_hours = sum(file_ages) / len(file_ages)
            oldest_age_hours = max(file_ages)
            newest_age_hours = min(file_ages)
            
            self.stdout.write(f'  Average file age: {self.format_hours(avg_age_hours)}')
            self.stdout.write(f'  Oldest file age: {self.format_hours(oldest_age_hours)}')
            self.stdout.write(f'  Newest file age: {self.format_hours(newest_age_hours)}')

        # Detailed file information
        if detailed:
            self.stdout.write('\n  Detailed File Information:')
            
            # Sort files by modification time (newest first)
            file_info = []
            for file_path in cache_files:
                if os.path.exists(file_path):
                    stat = os.stat(file_path)
                    mtime = datetime.fromtimestamp(stat.st_mtime)
                    size = stat.st_size
                    filename = os.path.basename(file_path)
                    age_hours = (now - mtime).total_seconds() / 3600
                    
                    file_info.append({
                        'filename': filename,
                        'size': size,
                        'mtime': mtime,
                        'age_hours': age_hours
                    })
            
            # Sort by age (newest first)
            file_info.sort(key=lambda x: x['age_hours'])
            
            # Show first 10 files
            for i, info in enumerate(file_info[:10]):
                self.stdout.write(
                    f'    {info["filename"][:20]}... | '
                    f'{self.format_bytes(info["size"]):>8} | '
                    f'{self.format_hours(info["age_hours"])}'
                )
            
            if len(file_info) > 10:
                self.stdout.write(f'    ... and {len(file_info) - 10} more files')

        self.stdout.write('')

    def show_cache_durations(self):
        """Show cache duration settings."""
        self.stdout.write(self.style.HTTP_INFO('Cache Duration Settings:'))
        
        # Import the constants from api_manager
        try:
            from recipes.api_manager import RECIPE_CACHE_DURATION, API_RATE_LIMIT_CACHE_DURATION
            
            self.stdout.write(f'  Recipe cache duration: {self.format_seconds(RECIPE_CACHE_DURATION)}')
            self.stdout.write(f'  API rate limit cache: {self.format_seconds(API_RATE_LIMIT_CACHE_DURATION)}')
            
        except ImportError:
            self.stdout.write('  Could not import cache duration constants')
        
        self.stdout.write('')

    def format_bytes(self, bytes_value):
        """Format bytes into human readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"

    def format_hours(self, hours):
        """Format hours into human readable format."""
        if hours < 1:
            return f"{hours * 60:.0f} minutes"
        elif hours < 24:
            return f"{hours:.1f} hours"
        else:
            days = hours / 24
            return f"{days:.1f} days"

    def format_seconds(self, seconds):
        """Format seconds into human readable format."""
        if seconds < 60:
            return f"{seconds} seconds"
        elif seconds < 3600:
            return f"{seconds // 60} minutes"
        elif seconds < 86400:
            return f"{seconds // 3600} hours"
        else:
            return f"{seconds // 86400} days"
