"""
Django management command to check Redis health and cache statistics.
Usage: python manage.py redis_health [--stats] [--test-cache]
"""

from django.core.management.base import BaseCommand
from recipes.cache_utils import check_redis_health, get_cache_stats, safe_cache_set, safe_cache_get
import json


class Command(BaseCommand):
    help = 'Check Redis health and display cache statistics'

    def add_arguments(self, parser):
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show detailed Redis statistics',
        )
        parser.add_argument(
            '--test-cache',
            action='store_true',
            help='Test cache operations (set/get/delete)',
        )

    def handle(self, *args, **options):
        show_stats = options.get('stats')
        test_cache = options.get('test_cache')

        self.stdout.write(self.style.HTTP_INFO('=== Redis Health Check ===\n'))

        # Check Redis health
        health = check_redis_health()
        self.display_health_status(health)

        if show_stats and health['redis_available']:
            self.stdout.write('\n' + self.style.HTTP_INFO('=== Redis Statistics ==='))
            stats = get_cache_stats()
            self.display_cache_stats(stats)

        if test_cache:
            self.stdout.write('\n' + self.style.HTTP_INFO('=== Cache Operation Test ==='))
            self.test_cache_operations()

    def display_health_status(self, health):
        """Display Redis health status."""
        if health['redis_available']:
            self.stdout.write(
                self.style.SUCCESS(f'✓ Redis is available')
            )
            self.stdout.write(f'  Connection time: {health["connection_time"]}ms')
            self.stdout.write(f'  Cache backend: {health["cache_backend"]}')
        else:
            self.stdout.write(
                self.style.ERROR(f'✗ Redis is not available')
            )
            self.stdout.write(f'  Error: {health["error"]}')

    def display_cache_stats(self, stats):
        """Display Redis cache statistics."""
        if stats['redis_available']:
            self.stdout.write(f'  Memory usage: {stats["memory_usage"]}')
            self.stdout.write(f'  Connected clients: {stats["connected_clients"]}')
            self.stdout.write(f'  Total commands: {stats["total_commands_processed"]}')
            self.stdout.write(f'  Keyspace hits: {stats["keyspace_hits"]}')
            self.stdout.write(f'  Keyspace misses: {stats["keyspace_misses"]}')
            if stats['hit_rate'] is not None:
                self.stdout.write(f'  Hit rate: {stats["hit_rate"]}%')
        else:
            self.stdout.write(f'  Error: {stats["error"]}')

    def test_cache_operations(self):
        """Test basic cache operations."""
        test_key = "redis_health_test"
        test_value = {"test": "data", "timestamp": "2024-01-01"}

        # Test SET operation
        self.stdout.write('Testing cache SET operation...')
        set_result = safe_cache_set(test_key, test_value, timeout=60)
        if set_result:
            self.stdout.write(self.style.SUCCESS('  ✓ Cache SET successful'))
        else:
            self.stdout.write(self.style.ERROR('  ✗ Cache SET failed'))
            return

        # Test GET operation
        self.stdout.write('Testing cache GET operation...')
        get_result = safe_cache_get(test_key)
        if get_result == test_value:
            self.stdout.write(self.style.SUCCESS('  ✓ Cache GET successful'))
        else:
            self.stdout.write(self.style.ERROR('  ✗ Cache GET failed or returned wrong value'))

        # Test cache expiration (optional)
        self.stdout.write('Cache operations test completed.')
