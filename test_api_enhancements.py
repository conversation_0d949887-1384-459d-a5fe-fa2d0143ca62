#!/usr/bin/env python3
"""
Test script for the enhanced API management system.
This script tests the new features implemented in recipes/api_manager.py.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'recipe_finder.settings')
django.setup()

from recipes.api_manager import (
    search_recipes,
    get_nutrition_info,
    comprehensive_recipe_search,
    get_api_health_status,
    enrich_recipes_with_nutrition,
    filter_recipes_by_nutrition,
    _spoonacular_to_tasty_fallback,
    _clean_ingredient_name,
    _analyze_nutritional_content
)

def test_basic_search():
    """Test basic recipe search with fallback system."""
    print("🔍 Testing basic recipe search...")
    
    try:
        results = search_recipes("chicken pasta", filters={"cuisine": "italian"})
        print(f"✅ Found {len(results)} recipes")
        
        if results:
            first_recipe = results[0]
            print(f"   First recipe: {first_recipe.get('title', 'Unknown')}")
            print(f"   Source: {first_recipe.get('source', 'Unknown')}")
        
        return True
    except Exception as e:
        print(f"❌ Basic search failed: {e}")
        return False

def test_nutrition_info():
    """Test enhanced nutrition information retrieval."""
    print("\n🥗 Testing nutrition information...")
    
    try:
        ingredients = ["chicken breast", "broccoli", "olive oil"]
        nutrition = get_nutrition_info(ingredients, detailed=True)
        
        print(f"✅ Nutrition data source: {nutrition.get('source', 'Unknown')}")
        print(f"   Processed ingredients: {nutrition.get('processed_ingredients', 0)}/{nutrition.get('total_ingredients', 0)}")
        
        analysis = nutrition.get('analysis', {})
        if analysis:
            macros = analysis.get('macronutrients', {})
            if macros:
                print(f"   Calories: {macros.get('calories_per_serving', 0)}")
                print(f"   Protein: {macros.get('protein_percentage', 0)}%")
        
        return True
    except Exception as e:
        print(f"❌ Nutrition test failed: {e}")
        return False

def test_comprehensive_search():
    """Test comprehensive search with nutrition filtering."""
    print("\n🎯 Testing comprehensive search...")
    
    try:
        results = comprehensive_recipe_search(
            "healthy salad",
            include_nutrition=True,
            nutrition_filters={
                "max_calories": 400,
                "min_protein": 10
            }
        )
        
        print(f"✅ Comprehensive search completed")
        print(f"   Total recipes found: {results.get('total_found', 0)}")
        print(f"   Search time: {results.get('search_time', 0)}s")
        print(f"   APIs used: {', '.join(results.get('apis_used', []))}")
        print(f"   Nutrition included: {results.get('nutrition_included', False)}")
        print(f"   Filters applied: {results.get('filters_applied', False)}")
        
        return True
    except Exception as e:
        print(f"❌ Comprehensive search failed: {e}")
        return False

def test_api_health():
    """Test API health monitoring."""
    print("\n🏥 Testing API health status...")
    
    try:
        health = get_api_health_status()
        
        print(f"✅ API health check completed")
        overall = health.get('overall_health', {})
        print(f"   Overall health: {overall.get('health_percentage', 0)}%")
        print(f"   Available APIs: {overall.get('available_apis', 0)}/{overall.get('total_apis', 0)}")
        
        for api_name, api_status in health.get('apis', {}).items():
            status = "✅" if api_status.get('available', False) else "❌"
            print(f"   {status} {api_name.title()}: {api_status.get('status_code', 'N/A')}")
        
        return True
    except Exception as e:
        print(f"❌ API health test failed: {e}")
        return False

def test_fallback_system():
    """Test the Tasty API fallback system."""
    print("\n🔄 Testing fallback system...")
    
    try:
        # Test the fallback function directly
        results = _spoonacular_to_tasty_fallback("pasta recipes")
        
        print(f"✅ Fallback system test completed")
        print(f"   Fallback results: {len(results)} recipes")
        
        if results:
            print(f"   First fallback recipe: {results[0].get('title', 'Unknown')}")
        
        return True
    except Exception as e:
        print(f"❌ Fallback system test failed: {e}")
        return False

def test_ingredient_cleaning():
    """Test ingredient name cleaning for better API results."""
    print("\n🧹 Testing ingredient cleaning...")
    
    try:
        test_ingredients = [
            "2 cups chopped fresh broccoli",
            "1 lb ground chicken breast",
            "3 tbsp extra virgin olive oil",
            "1 large diced onion"
        ]
        
        print("✅ Ingredient cleaning test:")
        for ingredient in test_ingredients:
            cleaned = _clean_ingredient_name(ingredient)
            print(f"   '{ingredient}' → '{cleaned}'")
        
        return True
    except Exception as e:
        print(f"❌ Ingredient cleaning test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Enhanced API Management System Tests")
    print("=" * 60)
    
    tests = [
        test_basic_search,
        test_nutrition_info,
        test_comprehensive_search,
        test_api_health,
        test_fallback_system,
        test_ingredient_cleaning
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced API system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
