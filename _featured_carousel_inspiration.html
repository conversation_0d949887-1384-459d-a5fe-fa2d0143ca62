<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food Carousel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .carousel-container {
            max-width: 1200px;
            width: 100%;
            position: relative;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .carousel-wrapper {
            position: relative;
            height: 500px;
            overflow: hidden;
        }

        .carousel-track {
            display: flex;
            animation: continuousSlide 60s linear infinite;
            height: 100%;
        }

        @keyframes continuousSlide {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-500%);
            }
        }

        .carousel-track:hover {
            animation-play-state: paused;
        }

        .carousel-slide {
            min-width: 100%;
            display: flex;
            gap: 15px;
            padding: 20px;
            align-items: center;
        }

        .slide-item {
            flex: 1;
            height: 100%;
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .slide-item:hover {
            transform: translateY(-5px);
        }

        .slide-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            padding: 25px;
            color: white;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .slide-item:hover .slide-overlay {
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            .slide-overlay {
                transform: translateY(0);
                background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
                padding: 15px;
            }
        }

        .slide-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .slide-description {
            font-size: 0.9rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .slide-price {
            font-size: 1.2rem;
            font-weight: 600;
            color: #ff6b6b;
            margin-top: 10px;
        }

        .carousel-controls {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .carousel-controls:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-50%) scale(1.1);
        }

        .prev-btn {
            left: 20px;
        }

        .next-btn {
            right: 20px;
        }

        .carousel-indicators {
            display: flex;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: #ff6b6b;
            transform: scale(1.2);
        }

        .progress-bar {
            height: 3px;
            background: rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            width: 100%;
            animation: progressMove 60s linear infinite;
        }

        @keyframes progressMove {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(0); }
        }

        @media (max-width: 768px) {
            .carousel-wrapper {
                height: 400px;
            }

            .carousel-slide {
                gap: 10px;
                padding: 15px;
            }

            .slide-item:nth-child(n+3) {
                display: none;
            }

            .slide-title {
                font-size: 1.2rem;
            }

            .slide-description {
                font-size: 0.85rem;
            }

            .slide-overlay {
                padding: 20px;
            }

            .carousel-controls {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .carousel-wrapper {
                height: 350px;
            }

            .slide-title {
                font-size: 1.1rem;
            }

            .slide-description {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="carousel-container">
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        
        <div class="carousel-wrapper">
            <div class="carousel-track">
                <!-- Slide 1 -->
                <div class="carousel-slide">
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Margherita Pizza</h3>
                            <p class="slide-description">Classic Italian pizza with fresh mozzarella and basil</p>
                            <div class="slide-price">18</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Pepperoni Supreme</h3>
                            <p class="slide-description">Loaded with premium pepperoni and three-cheese blend</p>
                            <div class="slide-price">22</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1571407970349-bc81e7e96d47?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Quattro Stagioni</h3>
                            <p class="slide-description">Four seasons pizza with artichokes, ham, and mushrooms</p>
                            <div class="slide-price">24</div>
                        </div>
                    </div>
                </div>
                
                <!-- Slide 2 -->
                <div class="carousel-slide">
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Blueberry Stack</h3>
                            <p class="slide-description">Fluffy pancakes with fresh blueberries and maple syrup</p>
                            <div class="slide-price">14</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1528207776546-365bb710ee93?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Strawberry Delight</h3>
                            <p class="slide-description">Golden pancakes topped with fresh strawberries</p>
                            <div class="slide-price">16</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Chocolate Chip</h3>
                            <p class="slide-description">Classic pancakes with chocolate chips and whipped cream</p>
                            <div class="slide-price">15</div>
                        </div>
                    </div>
                </div>
                
                <!-- Slide 3 -->
                <div class="carousel-slide">
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Caesar Salad</h3>
                            <p class="slide-description">Crisp romaine with parmesan and house-made croutons</p>
                            <div class="slide-price">12</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Mediterranean Bowl</h3>
                            <p class="slide-description">Mixed greens with feta, olives, and balsamic dressing</p>
                            <div class="slide-price">16</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1540420773420-3366772f4999?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Avocado Toast</h3>
                            <p class="slide-description">Smashed avocado on sourdough with cherry tomatoes</p>
                            <div class="slide-price">11</div>
                        </div>
                    </div>
                </div>
                
                <!-- Slide 4 -->
                <div class="carousel-slide">
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Classic Burger</h3>
                            <p class="slide-description">Beef patty with lettuce, tomato, and special sauce</p>
                            <div class="slide-price">16</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1550547660-d9450f859349?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">BBQ Bacon Burger</h3>
                            <p class="slide-description">Smoky BBQ sauce with crispy bacon and onion rings</p>
                            <div class="slide-price">19</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Mushroom Swiss</h3>
                            <p class="slide-description">Sautéed mushrooms with melted Swiss cheese</p>
                            <div class="slide-price">17</div>
                        </div>
                    </div>
                </div>
                
                <!-- Slide 5 -->
                <div class="carousel-slide">
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1563379091339-03246963d51a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Chocolate Mousse</h3>
                            <p class="slide-description">Rich chocolate mousse with fresh berries</p>
                            <div class="slide-price">9</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Crème Brûlée</h3>
                            <p class="slide-description">Vanilla custard with caramelized sugar crust</p>
                            <div class="slide-price">8</div>
                        </div>
                    </div>
                    <div class="slide-item" style="background-image: url('https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Tiramisu</h3>
                            <p class="slide-description">Classic Italian dessert with coffee and mascarpone</p>
                            <div class="slide-price">10</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <button class="carousel-controls prev-btn" style="display: none;">‹</button>
            <button class="carousel-controls next-btn" style="display: none;">›</button>
        </div>
        
        <div class="carousel-indicators" style="display: none;">
            <span class="indicator active"></span>
            <span class="indicator"></span>
            <span class="indicator"></span>
            <span class="indicator"></span>
            <span class="indicator"></span>
        </div>
    </div>

    <script>
        // Remove all JavaScript since we're using CSS animation
        const carouselContainer = document.querySelector('.carousel-container');
        
        // Keep only the pause on hover functionality
        carouselContainer.addEventListener('mouseenter', () => {
            document.querySelector('.carousel-track').style.animationPlayState = 'paused';
        });
        
        carouselContainer.addEventListener('mouseleave', () => {
            document.querySelector('.carousel-track').style.animationPlayState = 'running';
        });
    </script>
</body>
</html>