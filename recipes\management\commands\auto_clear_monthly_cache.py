"""
Django management command for automatic monthly cache clearing.
This command should be scheduled to run on the last day of each month at midnight.

Usage: python manage.py auto_clear_monthly_cache

Scheduling examples:
- Cron: 0 0 28-31 * * [ $(date -d tomorrow +\\%d) -eq 1 ] && python manage.py auto_clear_monthly_cache
- Windows Task Scheduler: Schedule for last day of month at midnight
"""

from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.conf import settings
import os
import glob
import logging
from datetime import datetime, timedelta
import calendar


# Set up logging
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Automatically clear recipe cache on the last day of each month'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force cache clearing regardless of date',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleared without actually clearing',
        )

    def handle(self, *args, **options):
        force = options.get('force')
        dry_run = options.get('dry_run')

        # Check if today is the last day of the month (or force is used)
        if not force and not self.is_last_day_of_month():
            self.stdout.write(
                self.style.WARNING(
                    'Today is not the last day of the month. Use --force to clear anyway.'
                )
            )
            return

        # Log the cache clearing attempt
        logger.info('Starting automatic monthly cache clearing')

        try:
            # Show cache statistics before clearing
            stats = self.get_cache_stats()
            
            if dry_run:
                self.stdout.write(self.style.HTTP_INFO('DRY RUN - No cache will be cleared'))
                self.stdout.write(f'Would clear {stats["file_count"]} cache files ({stats["total_size_formatted"]})')
                return

            # Clear the cache
            cache.clear()
            
            # Log success
            logger.info(f'Successfully cleared {stats["file_count"]} cache files ({stats["total_size_formatted"]})')
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Monthly cache clearing completed successfully!\n'
                    f'Cleared {stats["file_count"]} cache files ({stats["total_size_formatted"]})'
                )
            )

        except Exception as e:
            error_msg = f'Error during automatic cache clearing: {e}'
            logger.error(error_msg)
            self.stdout.write(self.style.ERROR(error_msg))
            raise

    def is_last_day_of_month(self):
        """Check if today is the last day of the current month."""
        today = datetime.now().date()
        
        # Get the last day of the current month
        last_day = calendar.monthrange(today.year, today.month)[1]
        
        return today.day == last_day

    def get_cache_stats(self):
        """Get cache statistics before clearing."""
        stats = {
            'file_count': 0,
            'total_size': 0,
            'total_size_formatted': '0 B'
        }

        # For file-based cache, count files in cache directory
        cache_location = getattr(settings, 'CACHES', {}).get('default', {}).get('LOCATION')
        if cache_location and os.path.exists(cache_location):
            cache_files = glob.glob(os.path.join(cache_location, '*.djcache'))
            total_size = sum(os.path.getsize(f) for f in cache_files if os.path.exists(f))
            
            stats['file_count'] = len(cache_files)
            stats['total_size'] = total_size
            stats['total_size_formatted'] = self.format_bytes(total_size)

        return stats

    def format_bytes(self, bytes_value):
        """Format bytes into human readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"
