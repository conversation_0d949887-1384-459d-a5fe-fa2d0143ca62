<!-- Modern Quick Filters -->
<div class="quick-filters mb-4 animate-fade-in" x-data="quickFiltersData()" x-init="init()">
    <style>
        /* Scaled-down desktop approach for all screen sizes */
        .quick-filters {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quick-filters .btn-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .quick-filters .btn {
            border-radius: 20px;
            padding: 8px 20px;
            border: 1px solid #e9ecef;
            background: white;
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            white-space: nowrap;
            min-height: 44px;
        }

        .quick-filters .btn:hover {
            background: #f8f9fa;
            color: #4CAF50;
            border-color: #4CAF50;
        }

        .quick-filters .btn.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
            box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
        }

        /* Tablet scaling (768px - 992px) */
        @media (max-width: 992px) and (min-width: 768px) {
            .quick-filters .btn {
                padding: 6px 16px;
                font-size: 13px;
                border-radius: 18px;
                min-height: 40px;
            }

            .quick-filters .btn-group {
                gap: 6px;
            }
        }

        /* Mobile scaling (below 768px) */
        @media (max-width: 767px) {
            .quick-filters .btn {
                padding: 6px 14px;
                font-size: 12px;
                border-radius: 16px;
                min-height: 38px;
            }

            .quick-filters .btn-group {
                gap: 6px;
            }
        }

        /* Small mobile scaling (below 480px) */
        @media (max-width: 479px) {
            .quick-filters .btn {
                padding: 5px 12px;
                font-size: 11px;
                border-radius: 14px;
                min-height: 36px;
            }

            .quick-filters .btn-group {
                gap: 4px;
            }
        }
    </style>

    <div class="btn-group" role="group">
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="quickFilter === 'All' ? 'active' : ''" @click="handleQuickFilter('All')">All</button>
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="selectedFilters.includes('Trending') ? 'active' : ''"
            @click="handleQuickFilter('Trending')">Trending</button>
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="selectedFilters.includes('Quick & Easy') ? 'active' : ''"
            @click="handleQuickFilter('Quick & Easy')">Quick & Easy</button>
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="selectedFilters.includes('Budget') ? 'active' : ''"
            @click="handleQuickFilter('Budget')">Budget</button>
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="selectedFilters.includes('Family') ? 'active' : ''"
            @click="handleQuickFilter('Family')">Family</button>
    </div>
</div>

<script>
function quickFiltersData() {
    return {
        quickFilter: 'All',
        selectedFilters: [], // Array to store multiple selected filters
        pendingFilter: null,

        init() {
            // Initialize filters from URL parameters if present
            const urlParams = new URLSearchParams(window.location.search);
            const quickFilterParam = urlParams.get('quick_filter');

            if (quickFilterParam && quickFilterParam !== 'All') {
                // Parse comma-separated filters
                this.selectedFilters = quickFilterParam.split(',').map(f => f.trim()).filter(f => f);
                this.quickFilter = '';
            }

            // Update the main search form with current selection
            this.updateMainSearchFormFilters();
        },

        handleQuickFilter(filterType) {
            // Handle 'All' filter - clear all other selections
            if (filterType === 'All') {
                this.quickFilter = 'All';
                this.selectedFilters = [];
            } else {
                // Handle individual filter selection
                this.quickFilter = ''; // Clear 'All' selection

                // Toggle the filter in selectedFilters array
                const index = this.selectedFilters.indexOf(filterType);
                if (index > -1) {
                    // Filter is already selected, remove it
                    this.selectedFilters.splice(index, 1);
                } else {
                    // Filter is not selected, add it
                    this.selectedFilters.push(filterType);
                }

                // If no filters are selected, default back to 'All'
                if (this.selectedFilters.length === 0) {
                    this.quickFilter = 'All';
                }
            }

            // Apply haptic feedback on mobile for selection feedback
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            // Update the main search form to include selected filters
            this.updateMainSearchFormFilters();
        },

        updateMainSearchFormFilters() {
            // Find the main search form and update it with current filter selection
            const mainSearchForm = document.querySelector('#main-search-box form');
            if (mainSearchForm) {
                // Remove any existing quick_filter input
                const existingFilterInput = mainSearchForm.querySelector('input[name="quick_filter"]');
                if (existingFilterInput) {
                    existingFilterInput.remove();
                }

                // Add current filter selection as hidden input
                const filterValue = this.selectedFilters.length > 0 ? this.selectedFilters.join(',') : this.quickFilter;
                if (filterValue && filterValue !== 'All') {
                    const filterInput = document.createElement('input');
                    filterInput.type = 'hidden';
                    filterInput.name = 'quick_filter';
                    filterInput.value = filterValue;
                    mainSearchForm.appendChild(filterInput);
                }
            }
        },


    };
}
</script>
