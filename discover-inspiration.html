<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discover Recipes</title>
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #4CAF50;
            --accent-color: #C62828;
            --neutral-color: #FFF8E1;
            --primary-dark: #E55A2B;
            --secondary-dark: #3D8B3F;
            --text-on-primary: #FFFFFF;
            --text-on-secondary: #FFFFFF;
            --text-dark: #333333;
            --text-light: #666666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: var(--neutral-color);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .featured-recipe {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .featured-recipe-image {
            width: 150px;
            height: 150px;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
            flex-shrink: 0;
        }

        .featured-recipe-info {
            flex: 1;
            padding: 20px;
        }

        .featured-recipe-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        .featured-recipe-details {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .randomize-btn {
            background: var(--secondary-color);
            color: var(--text-on-secondary);
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            margin-bottom: 30px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .randomize-btn:hover {
            background: var(--secondary-dark);
            transform: translateY(-2px);
        }

        .quick-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .action-btn {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .action-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        }

        .countries-strip {
            display: flex;
            gap: 15px;
            margin-bottom: 40px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .country-chip {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            padding: 8px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            font-size: 0.9rem;
            color: var(--text-dark);
        }

        .country-chip:hover, .country-chip.active {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .recipes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 25px;
        }

        .recipe-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .recipe-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .recipe-image {
            width: 100%;
            height: 180px;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
        }

        .recipe-info {
            padding: 15px;
        }

        .recipe-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        .recipe-country {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .recipe-category {
            background: var(--neutral-color);
            color: var(--text-dark);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            display: inline-block;
            border: 1px solid var(--primary-color);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: var(--text-light);
            font-size: 1.1rem;
        }

            @media (max-width: 768px) {
                .header h1 {
                    font-size: 2rem;
                }
                
                .featured-recipe {
                    flex-direction: column;
                    text-align: center;
                }
                
                .featured-recipe-image {
                    width: 100%;
                    height: 200px;
                }
                
                .quick-actions {
                    flex-direction: column;
                    align-items: center;
                }
                
                .action-btn {
                    width: 200px;
                }
            }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Discover Recipes</h1>
        </div>

        <div id="featured-recipe" class="featured-recipe">
            <!-- Featured random recipe will be loaded here -->
        </div>

        <button class="randomize-btn" onclick="loadFeaturedRecipe()">🎲 Try Another Random Recipe</button>

        <div class="countries-strip">
            <div class="country-chip active" onclick="loadCountry('all')">All</div>
            <div class="country-chip" onclick="loadCountry('Italian')">🇮🇹 Italian</div>
            <div class="country-chip" onclick="loadCountry('Mexican')">🇲🇽 Mexican</div>
            <div class="country-chip" onclick="loadCountry('Indian')">🇮🇳 Indian</div>
            <div class="country-chip" onclick="loadCountry('Chinese')">🇨🇳 Chinese</div>
            <div class="country-chip" onclick="loadCountry('French')">🇫🇷 French</div>
            <div class="country-chip" onclick="loadCountry('Japanese')">🇯🇵 Japanese</div>
            <div class="country-chip" onclick="loadCountry('Thai')">🇹🇭 Thai</div>
        </div>

        <div id="recipes-container" class="recipes-grid">
            <!-- Recipes will be loaded here -->
        </div>

        <div id="loading" class="loading" style="display: none;">
            Loading delicious recipes...
        </div>
    </div>

    <script>
        let currentCountry = 'all';

        // Load recipes immediately when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadFeaturedRecipe();
            loadInitialRecipes();
        });

        async function loadFeaturedRecipe() {
            try {
                const response = await fetch('https://www.themealdb.com/api/json/v1/1/random.php');
                const data = await response.json();
                if (data.meals && data.meals[0]) {
                    displayFeaturedRecipe(data.meals[0]);
                }
            } catch (error) {
                console.log('Error loading featured recipe:', error);
            }
        }

        function displayFeaturedRecipe(recipe) {
            const container = document.getElementById('featured-recipe');
            container.innerHTML = `
                <div class="featured-recipe-image">
                    <img src="${recipe.strMealThumb}" alt="${recipe.strMeal}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 15px 0 0 15px;">
                </div>
                <div class="featured-recipe-info">
                    <div class="featured-recipe-title">${recipe.strMeal}</div>
                    <div class="featured-recipe-details">
                        ${recipe.strArea} • ${recipe.strCategory}
                    </div>
                    <p style="color: var(--text-light); font-size: 0.9rem; line-height: 1.4;">
                        ${recipe.strInstructions ? recipe.strInstructions.substring(0, 150) + '...' : 'A delicious recipe waiting to be discovered!'}
                    </p>
                </div>
            `;
            container.style.cursor = 'pointer';
            container.onclick = () => viewRecipe(recipe.idMeal);
        }

        async function loadInitialRecipes() {
            showLoading();
            
            // Load a mix of random recipes from different countries
            const countries = ['Italian', 'Mexican', 'Indian', 'Chinese'];
            const allRecipes = [];
            
            for (const country of countries) {
                try {
                    const response = await fetch(`https://www.themealdb.com/api/json/v1/1/filter.php?a=${country}`);
                    const data = await response.json();
                    if (data.meals) {
                        // Get first 3 recipes from each country
                        allRecipes.push(...data.meals.slice(0, 3));
                    }
                } catch (error) {
                    console.log('Error loading recipes:', error);
                }
            }
            
            displayRecipes(allRecipes);
            hideLoading();
        }

        async function loadCountry(country) {
            // Update active chip
            document.querySelectorAll('.country-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            event.target.classList.add('active');
            
            currentCountry = country;
            showLoading();
            
            if (country === 'all') {
                loadInitialRecipes();
                return;
            }
            
            try {
                const response = await fetch(`https://www.themealdb.com/api/json/v1/1/filter.php?a=${country}`);
                const data = await response.json();
                displayRecipes(data.meals || []);
            } catch (error) {
                console.log('Error loading country recipes:', error);
                displayRecipes([]);
            }
            
            hideLoading();
        }

        async function getRandomMeals() {
            showLoading();
            const randomRecipes = [];
            
            // Get 8 random recipes
            for (let i = 0; i < 8; i++) {
                try {
                    const response = await fetch('https://www.themealdb.com/api/json/v1/1/random.php');
                    const data = await response.json();
                    if (data.meals) {
                        randomRecipes.push(data.meals[0]);
                    }
                } catch (error) {
                    console.log('Error getting random meal:', error);
                }
            }
            
            displayRecipes(randomRecipes);
            hideLoading();
        }

        async function browseByLetter(letter) {
            showLoading();
            
            try {
                const response = await fetch(`https://www.themealdb.com/api/json/v1/1/search.php?f=${letter}`);
                const data = await response.json();
                displayRecipes(data.meals || []);
            } catch (error) {
                console.log('Error browsing by letter:', error);
                displayRecipes([]);
            }
            
            hideLoading();
        }

        function displayRecipes(recipes) {
            const container = document.getElementById('recipes-container');
            
            if (!recipes || recipes.length === 0) {
                container.innerHTML = '<div class="loading">No recipes found</div>';
                return;
            }
            
            container.innerHTML = recipes.map(recipe => `
                <div class="recipe-card" onclick="viewRecipe('${recipe.idMeal}')">
                    <div class="recipe-image">
                        <img src="${recipe.strMealThumb}" alt="${recipe.strMeal}" style="width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <div class="recipe-info">
                        <div class="recipe-title">${recipe.strMeal}</div>
                        <div class="recipe-country">${recipe.strArea || 'International'}</div>
                        <div class="recipe-category">${recipe.strCategory || 'Main Course'}</div>
                    </div>
                </div>
            `).join('');
        }

        function viewRecipe(id) {
            // This would navigate to the recipe detail page
            console.log('Viewing recipe:', id);
            alert(`This would show the full recipe details for meal ID: ${id}`);
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('recipes-container').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('recipes-container').style.display = 'grid';
        }
    </script>
</body>
</html>