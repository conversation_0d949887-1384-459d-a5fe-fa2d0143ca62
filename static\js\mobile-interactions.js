// Mobile-Specific Interactions for Recipe Finder

document.addEventListener('DOMContentLoaded', function() {
    // Mobile-specific enhancements
    if (window.innerWidth <= 768) {
        initMobileInteractions();
    }
    
    // Re-initialize on resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            initMobileInteractions();
        }
    });
});

function initMobileInteractions() {
    // Add touch feedback to buttons
    addTouchFeedback();
    
    // Initialize swipe gestures for quick filters
    initSwipeGestures();
    
    // Add pull-to-refresh functionality
    initPullToRefresh();
    
    // Optimize form inputs for mobile
    optimizeMobileInputs();
    
    // Add haptic feedback
    addHapticFeedback();
}

function addTouchFeedback() {
    const buttons = document.querySelectorAll('.btn, .search-btn-primary, .quick-filters .btn');
    
    buttons.forEach(button => {
        // Add touch start effect
        button.addEventListener('touchstart', function(e) {
            this.style.transform = 'scale(0.98)';
            this.style.transition = 'transform 0.1s ease';
        }, { passive: true });
        
        // Remove touch effect
        button.addEventListener('touchend', function(e) {
            setTimeout(() => {
                this.style.transform = '';
                this.style.transition = 'transform 0.2s ease';
            }, 100);
        }, { passive: true });
        
        // Handle touch cancel
        button.addEventListener('touchcancel', function(e) {
            this.style.transform = '';
            this.style.transition = 'transform 0.2s ease';
        }, { passive: true });
    });
}

function initSwipeGestures() {
    const quickFiltersContainer = document.querySelector('.horizontal-quick-filters');
    if (!quickFiltersContainer) return;
    
    let startX = 0;
    let scrollLeft = 0;
    let isDown = false;
    
    quickFiltersContainer.addEventListener('touchstart', function(e) {
        isDown = true;
        startX = e.touches[0].pageX - quickFiltersContainer.offsetLeft;
        scrollLeft = quickFiltersContainer.scrollLeft;
    }, { passive: true });
    
    quickFiltersContainer.addEventListener('touchmove', function(e) {
        if (!isDown) return;
        e.preventDefault();
        const x = e.touches[0].pageX - quickFiltersContainer.offsetLeft;
        const walk = (x - startX) * 2;
        quickFiltersContainer.scrollLeft = scrollLeft - walk;
    });
    
    quickFiltersContainer.addEventListener('touchend', function() {
        isDown = false;
    }, { passive: true });
}

function initPullToRefresh() {
    let startY = 0;
    let pullDistance = 0;
    const threshold = 100;
    let isPulling = false;
    
    const mainContent = document.querySelector('.main-content');
    if (!mainContent) return;
    
    // Create pull indicator
    const pullIndicator = document.createElement('div');
    pullIndicator.className = 'pull-refresh-indicator';
    pullIndicator.innerHTML = `
        <div class="pull-refresh-content">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23 4 23 10 17 10"></polyline>
                <polyline points="1 20 1 14 7 14"></polyline>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
            </svg>
            <span>Pull to refresh</span>
        </div>
    `;
    pullIndicator.style.cssText = `
        position: fixed;
        top: -80px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--primary-500);
        color: white;
        padding: 12px 20px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        z-index: 1000;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    `;
    document.body.appendChild(pullIndicator);
    
    mainContent.addEventListener('touchstart', function(e) {
        if (window.scrollY === 0) {
            startY = e.touches[0].clientY;
            isPulling = true;
        }
    }, { passive: true });
    
    mainContent.addEventListener('touchmove', function(e) {
        if (!isPulling || window.scrollY > 0) return;
        
        pullDistance = e.touches[0].clientY - startY;
        
        if (pullDistance > 0) {
            e.preventDefault();
            const progress = Math.min(pullDistance / threshold, 1);
            pullIndicator.style.top = `${-80 + (progress * 100)}px`;
            pullIndicator.style.opacity = progress;
            
            if (pullDistance > threshold) {
                pullIndicator.querySelector('span').textContent = 'Release to refresh';
                pullIndicator.style.background = 'var(--primary-600)';
            } else {
                pullIndicator.querySelector('span').textContent = 'Pull to refresh';
                pullIndicator.style.background = 'var(--primary-500)';
            }
        }
    });
    
    mainContent.addEventListener('touchend', function() {
        if (isPulling && pullDistance > threshold) {
            // Trigger refresh
            pullIndicator.querySelector('span').textContent = 'Refreshing...';
            pullIndicator.style.top = '20px';
            
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            pullIndicator.style.top = '-80px';
            pullIndicator.style.opacity = '0';
        }
        
        isPulling = false;
        pullDistance = 0;
    }, { passive: true });
}

function optimizeMobileInputs() {
    const inputs = document.querySelectorAll('input[type="text"], textarea, select');
    
    inputs.forEach(input => {
        // Prevent zoom on focus for iOS
        input.addEventListener('focus', function() {
            if (this.type === 'text' || this.tagName === 'TEXTAREA') {
                this.style.fontSize = '16px';
            }
        });
        
        // Add mobile-friendly attributes
        if (input.type === 'text' && input.name === 'search_query') {
            input.setAttribute('autocomplete', 'off');
            input.setAttribute('autocorrect', 'off');
            input.setAttribute('autocapitalize', 'off');
            input.setAttribute('spellcheck', 'false');
        }
    });
}

function addHapticFeedback() {
    // Add haptic feedback for supported devices
    const interactiveElements = document.querySelectorAll('.btn, .search-btn-primary, .quick-filters .btn');
    
    interactiveElements.forEach(element => {
        element.addEventListener('click', function() {
            // Light haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        });
    });
    
    // Stronger feedback for important actions
    const primaryActions = document.querySelectorAll('.search-btn-primary');
    primaryActions.forEach(element => {
        element.addEventListener('click', function() {
            if (navigator.vibrate) {
                navigator.vibrate([50, 50, 50]);
            }
        });
    });
}

// Smooth scrolling for quick filters
function smoothScrollToFilter(element) {
    const container = element.closest('.horizontal-quick-filters');
    if (container) {
        const elementRect = element.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        if (elementRect.left < containerRect.left || elementRect.right > containerRect.right) {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center'
            });
        }
    }
}

// Add scroll indicators for horizontal scrollable areas
function addScrollIndicators() {
    const scrollableContainers = document.querySelectorAll('.horizontal-quick-filters');
    
    scrollableContainers.forEach(container => {
        const indicator = document.createElement('div');
        indicator.className = 'scroll-indicator';
        indicator.innerHTML = '→';
        indicator.style.cssText = `
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(to right, transparent, var(--background));
            padding: 8px;
            font-size: 18px;
            color: var(--text-muted);
            pointer-events: none;
            transition: opacity 0.3s ease;
        `;
        
        container.style.position = 'relative';
        container.appendChild(indicator);
        
        // Hide indicator when scrolled to end
        container.addEventListener('scroll', function() {
            const isAtEnd = this.scrollLeft >= (this.scrollWidth - this.clientWidth - 10);
            indicator.style.opacity = isAtEnd ? '0' : '1';
        });
        
        // Initial check
        const isAtEnd = container.scrollLeft >= (container.scrollWidth - container.clientWidth - 10);
        indicator.style.opacity = isAtEnd ? '0' : '1';
    });
}

// Initialize scroll indicators when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (window.innerWidth <= 768) {
        setTimeout(addScrollIndicators, 100);
    }
});

// Export functions for use in other scripts
window.mobileInteractions = {
    smoothScrollToFilter,
    addTouchFeedback,
    initSwipeGestures
};
