"""
Discover API Manager - Handles TheMealDB API calls for the discover page
Separate from api_manager.py to maintain clean separation of concerns
"""

import requests
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from django.core.cache import cache

logger = logging.getLogger(__name__)

# Cache durations
DAILY_ROTATION_CACHE_DURATION = 24 * 60 * 60  # 24 hours (1 day)
CATEGORY_CACHE_DURATION = 24 * 60 * 60  # 1 day (reduced from 7 days)
RANDOM_RECIPE_CACHE_DURATION = 60 * 60  # 1 hour

# TheMealDB API endpoints
THEMEALDB_BASE_URL = "https://www.themealdb.com/api/json/v1/1"
CATEGORIES_ENDPOINT = f"{THEMEALDB_BASE_URL}/list.php?c=list"
AREAS_ENDPOINT = f"{THEMEALDB_BASE_URL}/list.php?a=list"
RANDOM_ENDPOINT = f"{THEMEALDB_BASE_URL}/random.php"
FILTER_BY_CATEGORY_ENDPOINT = f"{THEMEALDB_BASE_URL}/filter.php?c="
FILTER_BY_AREA_ENDPOINT = f"{THEMEALDB_BASE_URL}/filter.php?a="

# Supported TheMealDB categories and areas
THEMEALDB_CATEGORIES = [
    "Beef", "Breakfast", "Chicken", "Dessert", "Goat", "Lamb", 
    "Miscellaneous", "Pasta", "Pork", "Seafood", "Side", "Starter", 
    "Vegan", "Vegetarian"
]

THEMEALDB_AREAS = [
    "American", "British", "Canadian", "Chinese", "Croatian", "Dutch",
    "Egyptian", "Filipino", "French", "Greek", "Indian", "Irish",
    "Italian", "Jamaican", "Japanese", "Kenyan", "Malaysian", "Mexican",
    "Moroccan", "Polish", "Portuguese", "Russian", "Spanish", "Thai",
    "Tunisian", "Turkish", "Ukrainian", "Uruguayan", "Vietnamese"
]


def safe_cache_get(key: str) -> Optional[Any]:
    """Safely get from cache with error handling"""
    try:
        return cache.get(key)
    except Exception as e:
        logger.warning(f"Cache get failed for key {key}: {e}")
        return None


def safe_cache_set(key: str, value: Any, timeout: int) -> None:
    """Safely set cache with error handling"""
    try:
        cache.set(key, value, timeout)
    except Exception as e:
        logger.warning(f"Cache set failed for key {key}: {e}")


def generate_daily_cache_key(base_key: str) -> str:
    """Generate a cache key that changes daily"""
    today = datetime.now().strftime("%Y-%m-%d")
    return f"{base_key}_{today}"


def get_random_recipe() -> Optional[Dict[str, Any]]:
    """Get a random recipe from TheMealDB"""
    cache_key = f"discover_random_recipe_{datetime.now().strftime('%Y-%m-%d_%H')}"
    cached = safe_cache_get(cache_key)
    if cached:
        return cached
    
    try:
        response = requests.get(RANDOM_ENDPOINT, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        if data.get('meals') and len(data['meals']) > 0:
            recipe = normalize_themealdb_recipe(data['meals'][0])
            safe_cache_set(cache_key, recipe, RANDOM_RECIPE_CACHE_DURATION)
            return recipe
    except Exception as e:
        logger.error(f"Error fetching random recipe: {e}")
    
    return None


def get_recipes_by_category(category: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Get recipes by category with daily rotation"""
    if category not in THEMEALDB_CATEGORIES:
        logger.warning(f"Invalid category: {category}")
        return []
    
    cache_key = generate_daily_cache_key(f"discover_category_{category}_{limit}")
    cached = safe_cache_get(cache_key)
    if cached:
        return cached
    
    try:
        response = requests.get(f"{FILTER_BY_CATEGORY_ENDPOINT}{category}", timeout=10)
        response.raise_for_status()
        data = response.json()
        
        if data.get('meals'):
            # Get all recipes and select 5 based on daily seed for rotation
            all_recipes = data['meals']
            daily_seed = int(datetime.now().strftime("%Y%m%d"))
            
            # Use daily seed to consistently select same 5 recipes for the day
            import random
            random.seed(daily_seed + hash(category))
            selected_recipes = random.sample(all_recipes, min(limit, len(all_recipes)))
            
            # Normalize the recipes
            normalized_recipes = [normalize_themealdb_recipe(recipe) for recipe in selected_recipes]
            safe_cache_set(cache_key, normalized_recipes, DAILY_ROTATION_CACHE_DURATION)
            return normalized_recipes
    except Exception as e:
        logger.error(f"Error fetching recipes for category {category}: {e}")
    
    return []


def get_recipes_by_area(area: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Get recipes by area/country with daily rotation"""
    if area not in THEMEALDB_AREAS:
        logger.warning(f"Invalid area: {area}")
        return []
    
    cache_key = generate_daily_cache_key(f"discover_area_{area}_{limit}")
    cached = safe_cache_get(cache_key)
    if cached:
        return cached
    
    try:
        response = requests.get(f"{FILTER_BY_AREA_ENDPOINT}{area}", timeout=10)
        response.raise_for_status()
        data = response.json()
        
        if data.get('meals'):
            # Get all recipes and select 5 based on daily seed for rotation
            all_recipes = data['meals']
            daily_seed = int(datetime.now().strftime("%Y%m%d"))
            
            # Use daily seed to consistently select same 5 recipes for the day
            import random
            random.seed(daily_seed + hash(area))
            selected_recipes = random.sample(all_recipes, min(limit, len(all_recipes)))
            
            # Normalize the recipes
            normalized_recipes = [normalize_themealdb_recipe(recipe) for recipe in selected_recipes]
            safe_cache_set(cache_key, normalized_recipes, DAILY_ROTATION_CACHE_DURATION)
            return normalized_recipes
    except Exception as e:
        logger.error(f"Error fetching recipes for area {area}: {e}")
    
    return []


def get_all_categories() -> List[str]:
    """Get all available categories from TheMealDB"""
    cache_key = "discover_all_categories"
    cached = safe_cache_get(cache_key)
    if cached:
        return cached
    
    try:
        response = requests.get(CATEGORIES_ENDPOINT, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        if data.get('meals'):
            categories = [meal['strCategory'] for meal in data['meals']]
            safe_cache_set(cache_key, categories, CATEGORY_CACHE_DURATION)
            return categories
    except Exception as e:
        logger.error(f"Error fetching categories: {e}")
    
    return THEMEALDB_CATEGORIES  # Fallback to hardcoded list


def get_all_areas() -> List[str]:
    """Get all available areas from TheMealDB"""
    cache_key = "discover_all_areas"
    cached = safe_cache_get(cache_key)
    if cached:
        return cached
    
    try:
        response = requests.get(AREAS_ENDPOINT, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        if data.get('meals'):
            areas = [meal['strArea'] for meal in data['meals']]
            safe_cache_set(cache_key, areas, CATEGORY_CACHE_DURATION)
            return areas
    except Exception as e:
        logger.error(f"Error fetching areas: {e}")
    
    return THEMEALDB_AREAS  # Fallback to hardcoded list


def normalize_themealdb_recipe(recipe: Dict[str, Any]) -> Dict[str, Any]:
    """Normalize TheMealDB recipe to standard format"""
    return {
        'id': f"themealdb:{recipe.get('idMeal', '')}",
        'title': recipe.get('strMeal', 'Unknown Recipe'),
        'image': recipe.get('strMealThumb', ''),
        'source': 'themealdb',
        'source_id': recipe.get('idMeal', ''),
        'category': recipe.get('strCategory', ''),
        'area': recipe.get('strArea', ''),
        'instructions': recipe.get('strInstructions', ''),
        'youtube': recipe.get('strYoutube', ''),
        'tags': recipe.get('strTags', '').split(',') if recipe.get('strTags') else [],
        'ingredients': extract_ingredients(recipe),
        'ready_in_minutes': None,  # TheMealDB doesn't provide cooking time
        'servings': None,  # TheMealDB doesn't provide servings
        'source_url': recipe.get('strSource', ''),
    }


def extract_ingredients(recipe: Dict[str, Any]) -> List[Dict[str, str]]:
    """Extract ingredients from TheMealDB recipe format"""
    ingredients = []
    for i in range(1, 21):  # TheMealDB has up to 20 ingredients
        ingredient = recipe.get(f'strIngredient{i}', '').strip()
        measure = recipe.get(f'strMeasure{i}', '').strip()
        
        if ingredient and ingredient.lower() not in ['', 'null']:
            ingredients.append({
                'name': ingredient,
                'amount': measure if measure and measure.lower() not in ['', 'null'] else ''
            })
    
    return ingredients


def get_discover_page_data() -> Dict[str, Any]:
    """Get all data needed for the discover page"""
    return {
        'featured_recipe': get_random_recipe(),
        'categories': get_all_categories(),
        'areas': get_all_areas(),
        'category_recipes': {
            category: get_recipes_by_category(category, 5)
            for category in get_all_categories()[:8]  # Limit to first 8 categories for performance
        }
    }


def clear_daily_cache():
    """Clear all daily rotation cache entries (useful for testing)"""
    try:
        # Get today's date string
        today = datetime.now().strftime("%Y-%m-%d")

        # Clear category caches
        for category in THEMEALDB_CATEGORIES:
            cache_key = f"discover_category_{category}_5_{today}"
            cache.delete(cache_key)
            logger.info(f"Cleared cache for category: {category}")

        # Clear area caches
        for area in THEMEALDB_AREAS:
            cache_key = f"discover_area_{area}_5_{today}"
            cache.delete(cache_key)
            logger.info(f"Cleared cache for area: {area}")

        # Clear random recipe cache
        current_hour = datetime.now().strftime('%Y-%m-%d_%H')
        cache_key = f"discover_random_recipe_{current_hour}"
        cache.delete(cache_key)
        logger.info("Cleared random recipe cache")

        return True
    except Exception as e:
        logger.error(f"Error clearing daily cache: {e}")
        return False


def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics for monitoring"""
    stats = {
        'categories_cached': 0,
        'areas_cached': 0,
        'random_recipe_cached': False,
        'cache_date': datetime.now().strftime("%Y-%m-%d"),
        'cache_hour': datetime.now().strftime('%H'),
    }

    try:
        today = datetime.now().strftime("%Y-%m-%d")
        current_hour = datetime.now().strftime('%Y-%m-%d_%H')

        # Check category caches
        for category in THEMEALDB_CATEGORIES:
            cache_key = f"discover_category_{category}_5_{today}"
            if safe_cache_get(cache_key) is not None:
                stats['categories_cached'] += 1

        # Check area caches
        for area in THEMEALDB_AREAS:
            cache_key = f"discover_area_{area}_5_{today}"
            if safe_cache_get(cache_key) is not None:
                stats['areas_cached'] += 1

        # Check random recipe cache
        cache_key = f"discover_random_recipe_{current_hour}"
        stats['random_recipe_cached'] = safe_cache_get(cache_key) is not None

    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        stats['error'] = str(e)

    return stats
